import os


# export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890
def set_proxy(proxy='http://127.0.0.1:7890', all_proxy='socks5://127.0.0.1:7890',
              no_proxy='localhost, 127.0.0.1, ***********/16, 10.0.0.0/8, **********/12, **********/16, ::1'):
    # 设置代理为本地代理
    os.environ['HTTP_PROXY'] = proxy
    os.environ['HTTPS_PROXY'] = proxy
    # 设置所有代理为本地ip
    os.environ['ALL_PROXY'] = all_proxy
    # 设置无代理的本地ip
    os.environ['NO_PROXY'] = no_proxy


def unset_proxy():
    os.environ.pop('HTTP_PROXY', None)
    os.environ.pop('HTTPS_PROXY', None)
    os.environ.pop('ALL_PROXY', None)
    os.environ.pop('NO_PROXY', None)
