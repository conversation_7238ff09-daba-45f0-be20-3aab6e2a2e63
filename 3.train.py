# 导入必要库
import torch
from datasets import Dataset
from modelscope import snapshot_download, AutoTokenizer
from swanlab.integration.transformers import SwanLab<PERSON>allback
from qwen_vl_utils import process_vision_info
from peft import LoraConfig, TaskType, get_peft_model, PeftModel
from transformers import (
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq,
    Qwen2VLForConditionalGeneration,
    AutoProcessor,
)
import swanlab
import json
import os


def process_func(example, processor, tokenizer):
    """数据预处理函数"""
    conversation = example["conversations"]
    image_file_path = conversation[0]["value"]
    output_content = conversation[1]["value"]

    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": image_file_path,
                    "resized_height": 500,
                    "resized_width": 500,  # ✅ FIX: 避免图像过度拉伸
                },
                {"type": "text", "text": prompt},
            ],
        }
    ]

    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    image_inputs, video_inputs = process_vision_info(messages)

    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
    )

    inputs = {key: value.tolist() for key, value in inputs.items()}
    instruction = inputs

    response = tokenizer(output_content, add_special_tokens=False)

    input_ids = instruction["input_ids"][0] + response["input_ids"] + [tokenizer.pad_token_id]
    attention_mask = instruction["attention_mask"][0] + response["attention_mask"] + [1]
    labels = [-100] * len(instruction["input_ids"][0]) + response["input_ids"] + [-100]  # ✅ FIX: pad部分标注为-100

    if len(input_ids) > MAX_LENGTH:
        input_ids = input_ids[:MAX_LENGTH]
        attention_mask = attention_mask[:MAX_LENGTH]
        labels = labels[:MAX_LENGTH]

    return {
        "input_ids": torch.tensor(input_ids),
        "attention_mask": torch.tensor(attention_mask),
        "labels": torch.tensor(labels),
        "pixel_values": torch.tensor(inputs["pixel_values"]),
        "image_grid_thw": torch.tensor(inputs["image_grid_thw"]).squeeze(0),
    }


def predict(messages, model, processor):
    """模型预测函数"""
    device = "cuda" if torch.cuda.is_available() else "cpu"  # ✅ FIX: 增加设备自动选择
    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    image_inputs, video_inputs = process_vision_info(messages)

    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
    ).to(device)

    generated_ids = model.generate(**inputs, max_new_tokens=512)  # ✅ FIX: 限制生成长度防止OOM
    generated_ids_trimmed = [out[len(ipt):] for ipt, out in zip(inputs.input_ids, generated_ids)]

    output_text = processor.batch_decode(generated_ids_trimmed, skip_special_tokens=True,
                                         clean_up_tokenization_spaces=False)
    return output_text[0]


def train(tokenizer, processor, origin_model):
    """模型训练函数"""
    origin_model.enable_input_require_grads()
    origin_model.config.use_cache = False
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"

    if not os.path.exists(train_dataset_json_path):
        raise FileNotFoundError(f"训练数据集 {train_dataset_json_path} 不存在")  # ✅ FIX: 增加健壮性判断

    train_ds = Dataset.from_json(train_dataset_json_path)
    train_dataset = train_ds.map(lambda ex: process_func(ex, processor, tokenizer))

    config1 = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=False,
        r=64,
        lora_alpha=16,
        lora_dropout=0.05,
    )
    train_peft_model = get_peft_model(origin_model, config1)

    args = TrainingArguments(
        output_dir=output_dir,
        per_device_train_batch_size=2,  # 从4降低为2，显存占用能减半
        gradient_accumulation_steps=8,  # 保持等效为 2x8 = 16
        logging_steps=10,
        logging_first_step=True,
        num_train_epochs=2,
        save_steps=100,
        learning_rate=1e-4,
        save_on_each_node=True,
        gradient_checkpointing=True,
        report_to="none",  # 从4降低为2，显存占用能减半
        fp16=True,  # 如果模型支持
    )

    swanlab_callback = SwanLabCallback(
        project="Qwen2-VL-ft-latexocr",
        experiment_name="7B-1kdata",
        config={
            "model": model_id,
            "train_dataset_json_path": train_dataset_json_path,
            "val_dataset_json_path": val_dataset_json_path,
            "output_dir": output_dir,
            "prompt": prompt,
            "train_data_number": len(train_ds),
            "token_max_length": MAX_LENGTH,
            "lora_rank": 64,
            "lora_alpha": 16,
            "lora_dropout": 0.1,
        },
    )

    trainer = Trainer(
        model=train_peft_model,
        args=args,
        train_dataset=train_dataset,
        data_collator=DataCollatorForSeq2Seq(tokenizer=tokenizer, padding=True),
        callbacks=[swanlab_callback],
    )
    trainer.train()


def predict_test(origin_model, processor, model_output_dir):
    """模型推理函数"""
    checkpoints = [int(d.split('-')[-1]) for d in os.listdir(model_output_dir) if d.startswith("checkpoint-")]
    if not checkpoints:
        raise ValueError("未找到任何 checkpoint")  # ✅ FIX: 空目录判断

    load_model_path = f"{model_output_dir}/checkpoint-{max(checkpoints)}" # ✅ FIX: 获取最新checkpoint
    print(f"load_model_path: {load_model_path}")

    val_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        inference_mode=True,
        r=64,
        lora_alpha=16,
        lora_dropout=0.05,
        bias="none",
    )
    val_peft_model = PeftModel.from_pretrained(origin_model, model_id=load_model_path, config=val_config)

    if not os.path.exists(val_dataset_json_path):
        raise FileNotFoundError(f"验证数据集 {val_dataset_json_path} 不存在")

    with open(val_dataset_json_path, "r") as f:
        test_dataset = json.load(f)

    test_image_list = []
    for item in test_dataset:
        image_file_path_t = item["conversations"][0]["value"]
        label = item["conversations"][1]["value"]

        messages_t = [{
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": image_file_path_t,
                    "resized_height": 224,  # ✅ FIX: 添加图片尺寸信息,原来为500
                    "resized_width": 224,  # ✅ FIX: 添加图片尺寸信息,原来为500
                },
                {"type": "text", "text": prompt}
            ]
        }]

        response_t = predict(messages_t, val_peft_model, processor)
        print(f"predict: {response_t}")
        print(f"gt: {label}\n")

        test_image_list.append(swanlab.Image(image_file_path_t, caption=response_t))

    return test_image_list


if __name__ == "__main__":
    import utils
    from transformers import AutoConfig

    utils.set_proxy()

    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"  # 缓解部分显存碎片问题。
    # 配置常量
    prompt = "你是一个LaText OCR助手,目标是读取用户输入的照片，转换成LaTex公式。"  # 模型提示
    model_id = "Qwen/Qwen2-VL-2B-Instruct"  # 模型名称
    local_model_path = "./Qwen/Qwen2-VL-2B-Instruct"  # 预训练模型的本地存放路径
    train_dataset_json_path = "latex_ocr_train.json"  # 训练数据集
    val_dataset_json_path = "latex_ocr_val.json"  # 验证数据集
    output_dir = "./output/Qwen2-VL-2B-LatexOCR"  # 训练后的模型保存路径
    MAX_LENGTH = 8192  # token的最大长度

    model_dir = snapshot_download(model_id, cache_dir="./", revision="master")  # 下载模型
    print(f'模型已经下载至本地，路径为：{model_dir}')

    tokenizer_t = AutoTokenizer.from_pretrained(local_model_path, use_fast=False, trust_remote_code=True)
    processor_t = AutoProcessor.from_pretrained(local_model_path)
    # origin_model_t = Qwen2VLForConditionalGeneration.from_pretrained(
    #     local_model_path, device_map="auto", torch_dtype=torch.bfloat16, trust_remote_code=True
    # )

    # 加载 config（可选，未来兼容性更强）
    config = AutoConfig.from_pretrained(local_model_path, trust_remote_code=True)
    origin_model_t = Qwen2VLForConditionalGeneration.from_pretrained(
        local_model_path,
        config=config,  # 显式传入配置（建议）
        device_map="auto",  # 可选，未来兼容性更强
        torch_dtype=torch.float16  # 可改为 bfloat16 取决于 GPU
    )

    # 模型训练
    train(tokenizer=tokenizer_t, processor=processor_t, origin_model=origin_model_t)

    # 模型推理
    test_image_list_t = predict_test(origin_model=origin_model_t, processor=processor_t, model_output_dir=output_dir)

    swanlab.log({"Prediction": test_image_list_t})
    swanlab.finish()
