<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LaTeX OCR 简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #409EFF;
            background-color: #f0f9ff;
        }
        .upload-area.drag-over {
            border-color: #409EFF;
            background-color: #e6f7ff;
        }
        .preview {
            margin: 20px 0;
            text-align: center;
        }
        .preview img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .input-group {
            display: flex;
            gap: 12px;
            margin: 20px 0;
        }
        .input-group input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .input-group button {
            padding: 12px 24px;
            background-color: #409EFF;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .input-group button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            background-color: #f4f4f8;
            border-radius: 8px;
            border-left: 4px solid #409EFF;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📸 LaTeX OCR 图像识别</h1>
        <p>上传包含数学公式的图片，AI 将识别并转换为 LaTeX 格式</p>
        
        <input type="file" id="fileInput" accept="image/*" style="display: none">
        
        <div class="upload-area" id="uploadArea">
            <div id="uploadContent">
                <div style="font-size: 48px; margin-bottom: 16px;">📁</div>
                <div style="font-size: 16px; margin-bottom: 8px;">点击或拖拽上传图片</div>
                <div style="font-size: 12px; color: #999;">支持 JPG、PNG 等格式</div>
            </div>
        </div>
        
        <div id="preview" class="preview" style="display: none;">
            <img id="previewImg" alt="预览图片">
            <div id="fileName" style="margin-top: 10px; color: #666;"></div>
        </div>
        
        <div class="input-group">
            <input 
                type="text" 
                id="promptInput" 
                placeholder="请输入提示词，例如：请识别这个数学公式"
                disabled
            >
            <button id="submitBtn" disabled>发送</button>
        </div>
        
        <div id="status"></div>
        <div id="results"></div>
    </div>

    <script>
        let selectedFile = null;
        
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const uploadContent = document.getElementById('uploadContent');
        const preview = document.getElementById('preview');
        const previewImg = document.getElementById('previewImg');
        const fileName = document.getElementById('fileName');
        const promptInput = document.getElementById('promptInput');
        const submitBtn = document.getElementById('submitBtn');
        const status = document.getElementById('status');
        const results = document.getElementById('results');
        
        // 点击上传区域
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // 文件选择
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        });
        
        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            const file = e.dataTransfer.files[0];
            if (file) {
                handleFile(file);
            }
        });
        
        // 处理文件
        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                showStatus('请选择图片文件', 'error');
                return;
            }
            
            if (file.size > 10 * 1024 * 1024) {
                showStatus('图片大小不能超过 10MB', 'error');
                return;
            }
            
            selectedFile = file;
            
            // 显示预览
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImg.src = e.target.result;
                fileName.textContent = `已选择: ${file.name}`;
                preview.style.display = 'block';
                
                // 更新上传区域
                uploadContent.innerHTML = `
                    <div style="color: #409EFF;">✅ 图片已上传</div>
                    <div style="font-size: 12px; color: #666; margin-top: 8px;">点击重新选择</div>
                `;
                
                // 启用输入框和按钮
                promptInput.disabled = false;
                updateSubmitButton();
            };
            reader.readAsDataURL(file);
            
            showStatus(`已选择图片: ${file.name}`, 'success');
        }
        
        // 更新提交按钮状态
        function updateSubmitButton() {
            submitBtn.disabled = !selectedFile || !promptInput.value.trim();
        }
        
        promptInput.addEventListener('input', updateSubmitButton);
        promptInput.addEventListener('keyup', (e) => {
            if (e.key === 'Enter' && !submitBtn.disabled) {
                submitForm();
            }
        });
        
        submitBtn.addEventListener('click', submitForm);
        
        // 提交表单
        async function submitForm() {
            if (!selectedFile || !promptInput.value.trim()) {
                showStatus('请上传图片并输入提示词', 'error');
                return;
            }
            
            submitBtn.disabled = true;
            submitBtn.textContent = '识别中...';
            showStatus('正在识别图片...', 'info');
            
            try {
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('prompt', promptInput.value);
                
                const response = await fetch('http://localhost:8000/predict', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showResult(result.markdown);
                    showStatus('识别完成！', 'success');
                    promptInput.value = ''; // 清空输入框
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('识别失败:', error);
                showStatus('识别失败，请重试', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '发送';
                updateSubmitButton();
            }
        }
        
        // 显示状态
        function showStatus(message, type) {
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                if (type !== 'error') {
                    status.innerHTML = '';
                }
            }, 3000);
        }
        
        // 显示结果
        function showResult(markdown) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.innerHTML = `
                <strong>识别结果:</strong><br>
                <div style="margin-top: 10px; font-family: monospace; white-space: pre-wrap;">${markdown}</div>
            `;
            results.appendChild(resultDiv);
            
            // 滚动到结果
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 页面加载完成后的初始化
        window.onload = () => {
            showStatus('请上传图片开始识别', 'info');
        };
    </script>
</body>
</html>
