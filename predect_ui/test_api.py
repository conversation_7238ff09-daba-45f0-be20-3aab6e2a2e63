#!/usr/bin/env python3
"""
测试 API 服务的简单脚本
"""
import requests
import json
from PIL import Image
import io
import base64

def test_api_connection():
    """测试 API 连接"""
    try:
        response = requests.get("http://localhost:8000")
        print(f"API 连接测试: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到 API 服务 (http://localhost:8000)")
        print("请确保后端服务正在运行")
        return False

def create_test_image():
    """创建一个简单的测试图片"""
    # 创建一个简单的白色背景图片，上面有黑色文字
    from PIL import Image, ImageDraw, ImageFont
    
    # 创建图片
    img = Image.new('RGB', (200, 100), color='white')
    draw = ImageDraw.Draw(img)
    
    # 添加一些文字（模拟数学公式）
    try:
        # 尝试使用默认字体
        draw.text((10, 30), "x^2 + y^2 = z^2", fill='black')
    except:
        # 如果字体加载失败，使用默认字体
        draw.text((10, 30), "x^2 + y^2 = z^2", fill='black')
    
    # 保存到内存
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    
    return img_buffer

def test_predict_endpoint():
    """测试预测端点"""
    if not test_api_connection():
        return False
    
    try:
        # 创建测试图片
        img_buffer = create_test_image()
        
        # 准备请求数据
        files = {
            'file': ('test.png', img_buffer, 'image/png')
        }
        data = {
            'prompt': '请识别这个数学公式'
        }
        
        print("正在测试预测端点...")
        response = requests.post("http://localhost:8000/predict", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print("预测成功!")
            print(f"结果: {result}")
            return True
        else:
            print(f"预测失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    print("=== API 服务测试 ===")
    
    # 测试基本连接
    if test_api_connection():
        print("✓ API 服务连接正常")
        
        # 测试预测功能
        if test_predict_endpoint():
            print("✓ 预测功能正常")
            print("\n=== 测试完成 ===")
            print("您的 LaTeX OCR 服务已准备就绪!")
        else:
            print("✗ 预测功能测试失败")
    else:
        print("✗ API 服务连接失败")
        print("请检查后端服务是否正在运行")
