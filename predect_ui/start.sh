#!/bin/bash

echo "=== LaTeX OCR 预测界面启动脚本 ==="

# 检查是否安装了 Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

# 检查是否安装了 conda
if ! command -v conda &> /dev/null; then
    echo "错误: 未找到 conda，请先安装 Anaconda 或 Miniconda"
    exit 1
fi

# 激活 tuning_model 虚拟环境
echo "正在激活 tuning_model 虚拟环境..."
source ~/workspace/anaconda3/etc/profile.d/conda.sh
conda activate tuning_model

# 检查虚拟环境是否激活成功
if [[ "$CONDA_DEFAULT_ENV" != "tuning_model" ]]; then
    echo "错误: 无法激活 tuning_model 虚拟环境"
    exit 1
fi

echo "已激活虚拟环境: $CONDA_DEFAULT_ENV"

# 安装前端依赖
echo "正在安装前端依赖..."
if [ ! -d "node_modules" ]; then
    npm install
fi

# 启动后端服务
echo "正在启动后端 API 服务..."
python 1.api.py &
API_PID=$!

# 等待后端启动
sleep 5

# 启动前端服务
echo "正在启动前端开发服务器..."
npm run dev &
FRONTEND_PID=$!

echo "=== 服务启动完成 ==="
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:8000"
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $API_PID $FRONTEND_PID; exit" INT
wait
