# GPU 内存问题解决方案

## 问题描述
在运行 LaTeX OCR 预测 API 时遇到 CUDA 内存不足错误：
```
CUDA out of memory. Tried to allocate 3.32 GiB. GPU 0 has a total capacity of 11.63 GiB
```

## 解决方案

### 关键发现
通过分析 `train.py` 中的成功配置，发现训练时使用了特定的内存优化设置，这些设置在推理时同样有效。

### 成功的配置要素

#### 1. 环境变量设置
```python
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
```

#### 2. 模型加载配置
```python
# 使用显式配置加载
config = AutoConfig.from_pretrained(model_name, trust_remote_code=True)
origin_model = Qwen2VLForConditionalGeneration.from_pretrained(
    model_name,
    config=config,  # 显式传入配置
    device_map="auto",
    torch_dtype=torch.float16  # 与 train.py 保持一致
)

# 关键的内存优化设置
origin_model.config.use_cache = False  # 禁用缓存
origin_model.enable_input_require_grads()  # 启用输入梯度
```

#### 3. 图像处理配置
```python
# 使用与训练时相同的图像尺寸
{
    "type": "image",
    "image": img_path,
    "resized_height": 224,  # 与 train.py 推理时保持一致
    "resized_width": 224,   # 与 train.py 推理时保持一致
}
```

#### 4. 生成配置
```python
# 使用与 train.py 完全相同的生成方式
generated_ids = val_peft_model.generate(**inputs, max_new_tokens=512)
generated_ids_trimmed = [out[len(ipt):] for ipt, out in zip(inputs.input_ids, generated_ids)]
output_text = processor.batch_decode(generated_ids_trimmed, skip_special_tokens=True,
                                     clean_up_tokenization_spaces=False)
```

### 对比分析

#### 失败的配置（之前）
- 使用复杂的生成参数（temperature, top_p, top_k）
- 启用模型缓存
- 使用不同的图像尺寸
- 缺少关键的内存优化设置

#### 成功的配置（现在）
- 完全复制 train.py 的配置
- 禁用模型缓存
- 使用相同的图像处理参数
- 设置正确的环境变量

## 测试结果

### 内存使用情况
- **GPU**: NVIDIA GeForce RTX 3080 Ti (11.63 GB)
- **使用率**: 0.0%（推理完成后自动清理）
- **状态**: ✅ 内存使用正常

### API 测试结果
```
=== API 服务测试 ===
✓ API 服务连接正常
✓ 预测功能正常
结果: x^2 + y^2 = z^2
=== 测试完成 ===
```

## 经验总结

### 关键教训
1. **保持配置一致性**：推理时的配置应该与训练时保持一致
2. **参考成功案例**：train.py 中的配置已经经过验证，直接复用最可靠
3. **环境变量重要**：`PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True` 对内存管理至关重要
4. **禁用缓存**：`use_cache=False` 是关键的内存优化设置

### 最佳实践
1. 在开发推理代码时，首先参考训练代码的配置
2. 使用相同的数据预处理参数
3. 保持模型加载方式的一致性
4. 设置正确的环境变量

## 文件修改记录

### 主要修改的文件
- `1.api.py`: 应用基于 train.py 的优化配置
- `README.md`: 更新内存优化说明
- `monitor_gpu.py`: 添加 GPU 内存监控工具

### 关键代码变更
1. 添加环境变量设置
2. 修改模型加载方式
3. 统一图像处理参数
4. 简化生成配置
5. 添加内存优化设置

## 结论
通过严格按照 train.py 中的成功配置来设置推理环境，完全解决了 GPU 内存不足的问题。这证明了配置一致性在深度学习项目中的重要性。
