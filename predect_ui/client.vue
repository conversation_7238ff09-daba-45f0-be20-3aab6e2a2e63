<!-- frontend/components/ChatInterface.vue -->
<template>
  <el-card class="chat-card">
    <div v-for="(msg, index) in messages" :key="index" class="chat-bubble" v-html="renderMarkdown(msg)" />

    <el-upload
      class="upload-box"
      drag
      action=""
      :http-request="uploadImage"
      :show-file-list="false"
    >
      <div>点击或拖拽上传图片</div>
    </el-upload>

    <el-input
      v-model="prompt"
      placeholder="请输入绘画提示词..."
      @keyup.enter="submitPrompt"
    />
    <el-button type="primary" @click="submitPrompt">发送</el-button>
  </el-card>
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'
import { marked } from 'marked'

const prompt = ref('')
const imageFile = ref(null)
const messages = ref([])

const uploadImage = async ({ file }) => {
  imageFile.value = file
}

const renderMarkdown = (text) => marked.parse(text)

const submitPrompt = async () => {
  if (!imageFile.value || !prompt.value) return alert("请上传图片并填写提示词")

  const formData = new FormData()
  formData.append("file", imageFile.value)
  formData.append("prompt", prompt.value)

  const res = await axios.post("http://localhost:8000/predict", formData)
  messages.value.push(res.data.markdown)
  prompt.value = ''
}
</script>

<style scoped>
.chat-card {
  width: 600px;
  margin: 0 auto;
}
.chat-bubble {
  background: #f4f4f8;
  padding: 10px;
  margin: 10px 0;
  border-radius: 8px;
}
</style>
