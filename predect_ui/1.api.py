# backend/predict_api.py
from fastapi import FastAP<PERSON>, UploadFile, Form
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image
import os, io, json
from transformers import AutoProcessor
from peft import PeftModel, LoraConfig, TaskType
from your_model_module import predict  # 假设你的模型推理函数叫 predict()


app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 可根据需要修改
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化模型，只加载一次
origin_model = ...  # 加载 base 模型
processor = AutoProcessor.from_pretrained("your/processor")
model_output_dir = "path/to/model/output"

checkpoints = [int(d.split('-')[-1]) for d in os.listdir(model_output_dir) if d.startswith("checkpoint-")]
if not checkpoints:
    raise ValueError("未找到任何 checkpoint")

load_model_path = f"{model_output_dir}/checkpoint-{max(checkpoints)}"
val_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    inference_mode=True,
    r=64,
    lora_alpha=16,
    lora_dropout=0.05,
    bias="none",
)
val_peft_model = PeftModel.from_pretrained(origin_model, model_id=load_model_path, config=val_config)


@app.post("/predict")
async def predict_single_image(file: UploadFile, prompt: str = Form(...)):
    # 保存临时图片
    contents = await file.read()
    img = Image.open(io.BytesIO(contents)).convert("RGB")
    img_path = f"/tmp/{file.filename}"
    img.save(img_path)

    # 构建输入
    messages = [{
        "role": "user",
        "content": [
            {
                "type": "image",
                "image": img_path,
                "resized_height": 224,
                "resized_width": 224,
            },
            {"type": "text", "text": prompt}
        ]
    }]

    # 执行模型推理
    response = predict(messages, val_peft_model, processor)

    return JSONResponse(content={"markdown": f"**Prompt:** {prompt}\n\n**AI Response:**\n\n{response}"})
