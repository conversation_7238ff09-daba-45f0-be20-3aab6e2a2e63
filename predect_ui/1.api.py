# backend/predict_api.py
from fastapi import FastAPI, UploadFile, Form
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image
import os, io, json
from transformers import AutoProcessor, Qwen2VLForConditionalGeneration
from peft import PeftModel, LoraConfig, TaskType
import torch
# from your_model_module import predict  # 假设你的模型推理函数叫 predict()


app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 可根据需要修改
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化模型，只加载一次
# 请根据实际情况修改以下路径
model_name = "/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/Qwen/Qwen2-VL-2B-Instruct"  # 本地基础模型路径
model_output_dir = "/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/output/Qwen2-VL-2B-LatexOCR"  # 训练输出目录

# 加载处理器和基础模型
processor = AutoProcessor.from_pretrained(model_name)
origin_model = Qwen2VLForConditionalGeneration.from_pretrained(
    model_name,
    torch_dtype=torch.float16,
    device_map="auto",
    low_cpu_mem_usage=True,
    offload_folder="./offload_cache"  # CPU 卸载缓存
)

# 查找最新的checkpoint
if os.path.exists(model_output_dir):
    checkpoints = [int(d.split('-')[-1]) for d in os.listdir(model_output_dir) if d.startswith("checkpoint-")]
    if checkpoints:
        load_model_path = f"{model_output_dir}/checkpoint-{max(checkpoints)}"
        val_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
            inference_mode=True,
            r=64,
            lora_alpha=16,
            lora_dropout=0.05,
            bias="none",
        )
        val_peft_model = PeftModel.from_pretrained(origin_model, model_id=load_model_path, config=val_config)
        print(f"已加载微调模型: {load_model_path}")
    else:
        val_peft_model = origin_model
        print("未找到checkpoint，使用基础模型")
else:
    val_peft_model = origin_model
    print("输出目录不存在，使用基础模型")


@app.post("/predict")
async def predict_single_image(file: UploadFile, prompt: str = Form(...)):
    # 保存临时图片
    contents = await file.read()
    img = Image.open(io.BytesIO(contents)).convert("RGB")
    img_path = f"/tmp/{file.filename}"
    img.save(img_path)

    # 构建输入
    messages = [{
        "role": "user",
        "content": [
            {
                "type": "image",
                "image": img_path,
                "resized_height": 224,
                "resized_width": 224,
            },
            {"type": "text", "text": prompt}
        ]
    }]

    # 执行模型推理
    try:
        # 处理输入
        text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        image_inputs, video_inputs = process_vision_info(messages)
        inputs = processor(
            text=[text],
            images=image_inputs if image_inputs else None,
            videos=video_inputs if video_inputs else None,
            padding=True,
            return_tensors="pt",
        )
        inputs = inputs.to("cuda" if torch.cuda.is_available() else "cpu")

        # 生成回复 - 使用更少的内存
        with torch.no_grad():
            generated_ids = val_peft_model.generate(
                **inputs,
                max_new_tokens=256,  # 减少生成长度
                do_sample=False,     # 使用贪心解码减少内存
                pad_token_id=processor.tokenizer.eos_token_id
            )
        generated_ids_trimmed = [
            out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        decoded_responses = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        response = decoded_responses[0] if decoded_responses else "无法生成回复"

        # 清理 GPU 内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # 清理临时文件
        if os.path.exists(img_path):
            os.remove(img_path)

        return JSONResponse(content={"markdown": f"**Prompt:** {prompt}\n\n**AI Response:**\n\n{response}"})

    except Exception as e:
        # 清理 GPU 内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # 清理临时文件
        if os.path.exists(img_path):
            os.remove(img_path)
        print(f"错误详情: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(content={"error": str(e)}, status_code=500)


def process_vision_info(messages):
    """处理视觉信息"""
    image_inputs = []
    video_inputs = []
    for message in messages:
        if isinstance(message, dict) and "content" in message:
            for content in message["content"]:
                if content.get("type") == "image":
                    image_path = content.get("image")
                    if image_path and os.path.exists(image_path):
                        image_inputs.append(Image.open(image_path).convert("RGB"))
    return image_inputs, video_inputs


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
