# LaTeX OCR 预测界面

这是一个基于 Vue.js + FastAPI 的 LaTeX OCR 图像识别界面。

## 项目结构

```
predect_ui/
├── src/
│   ├── components/
│   │   └── ChatInterface.vue    # 主要的聊天界面组件
│   ├── App.vue                  # 主应用组件
│   └── main.js                  # 应用入口文件
├── index.html                   # HTML 模板
├── package.json                 # 项目依赖配置
├── vite.config.js              # Vite 构建配置
├── 1.api.py                    # FastAPI 后端服务
└── README.md                   # 项目说明
```

## 安装和运行

### 前端 (Vue.js)

1. 安装依赖：
```bash
cd predect_ui
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

前端将在 http://localhost:3000 运行

### 后端 (FastAPI)

1. 安装 Python 依赖：
```bash
pip install fastapi uvicorn transformers torch peft pillow
```

2. 启动 API 服务：
```bash
python 1.api.py
```

后端将在 http://localhost:8000 运行

## 使用说明

1. 确保后端 API 服务正在运行
2. 打开前端界面
3. 拖拽或点击上传图片
4. 输入提示词（如："请识别这个数学公式"）
5. 点击发送按钮获取 LaTeX 识别结果

## 配置说明

在 `1.api.py` 中需要根据实际情况修改以下配置：

- `model_name`: 基础模型路径
- `model_output_dir`: 训练输出目录路径

## GPU 内存优化

如果遇到 CUDA 内存不足错误，可以使用以下优化方法：

### 方法 1: 使用优化启动脚本
```bash
../start_optimized.sh
```

### 方法 2: 手动设置环境变量
```bash
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
conda activate tuning_model
python 1.api.py
```

### 内存优化特性（基于 train.py 的成功配置）
- 使用 float16 精度减少内存占用
- 禁用模型缓存 (`use_cache=False`)
- 启用输入梯度 (`enable_input_require_grads()`)
- 使用与训练时相同的图像尺寸 (224x224)
- 设置环境变量 `PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True`
- 自动内存清理

## 注意事项

- 推荐 GPU 内存 ≥ 8GB，最佳 ≥ 12GB
- 图片会临时保存在 `/tmp/` 目录，处理完成后会自动删除
- 支持的图片格式：JPG, PNG 等常见格式
- 如果内存不足，系统会自动清理 GPU 缓存
