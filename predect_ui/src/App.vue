<template>
  <div id="app">
    <header class="app-header">
      <h1>LaTeX OCR 图像识别</h1>
    </header>
    <main class="app-main">
      <ChatInterface />
    </main>
  </div>
</template>

<script setup>
import ChatInterface from './components/ChatInterface.vue'
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background-color: #409EFF;
  color: white;
  text-align: center;
  height: 60px;
  line-height: 60px;
  margin: 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.app-main {
  background-color: #f5f5f5;
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}
</style>
