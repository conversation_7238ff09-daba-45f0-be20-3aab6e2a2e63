<template>
  <div id="app">
    <el-container direction="vertical">
      <el-header height="60px">
        <h1>LaTeX OCR 图像识别</h1>
      </el-header>
      <el-main>
        <ChatInterface />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import ChatInterface from './components/ChatInterface.vue'
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}

.el-header {
  background-color: #409EFF;
  color: white;
  text-align: center;
  line-height: 60px;
}

.el-main {
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
  padding: 20px;
}
</style>
