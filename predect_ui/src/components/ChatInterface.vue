<!-- frontend/components/ChatInterface.vue -->
<template>
  <el-card class="chat-card">
    <div v-for="(msg, index) in messages" :key="index" class="chat-bubble" v-html="renderMarkdown(msg)" />

    <!-- 图片上传区域 -->
    <el-upload
      class="upload-box"
      drag
      action=""
      :http-request="uploadImage"
      :show-file-list="false"
      accept="image/*"
    >
      <div v-if="!imageFile" class="upload-placeholder">
        <div class="upload-icon">📁</div>
        <div class="upload-text">点击或拖拽上传图片</div>
        <div class="upload-hint">支持 JPG、PNG 等格式</div>
      </div>
      <div v-else class="upload-success">
        <div class="success-icon">✅</div>
        <div class="success-text">已选择图片: {{ imageFile.name }}</div>
        <el-button size="small" type="text" @click="clearImage">重新选择</el-button>
      </div>
    </el-upload>

    <!-- 图片预览 -->
    <div v-if="imagePreview" class="image-preview">
      <img :src="imagePreview" alt="预览图片" />
      <div class="preview-overlay">
        <el-button size="small" @click="clearImage">删除</el-button>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <el-input
        v-model="prompt"
        placeholder="请输入提示词，例如：请识别这个数学公式"
        @keyup.enter="submitPrompt"
        :disabled="loading"
      />
      <el-button
        type="primary"
        @click="submitPrompt"
        :loading="loading"
        :disabled="!imageFile || !prompt.trim()"
      >
        {{ loading ? '识别中...' : '发送' }}
      </el-button>
    </div>
  </el-card>
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'
import { marked } from 'marked'
import { ElMessage } from 'element-plus'

const prompt = ref('')
const imageFile = ref(null)
const imagePreview = ref('')
const messages = ref([])
const loading = ref(false)

const uploadImage = async ({ file }) => {
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小 (10MB)
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过 10MB')
    return
  }

  imageFile.value = file

  // 创建图片预览
  const reader = new FileReader()
  reader.onload = (e) => {
    imagePreview.value = e.target.result
  }
  reader.readAsDataURL(file)

  ElMessage.success(`已选择图片: ${file.name}`)
}

const clearImage = () => {
  imageFile.value = null
  imagePreview.value = ''
}

const renderMarkdown = (text) => marked.parse(text)

const submitPrompt = async () => {
  if (!imageFile.value) {
    ElMessage.warning("请先上传图片")
    return
  }

  if (!prompt.value.trim()) {
    ElMessage.warning("请输入提示词")
    return
  }

  loading.value = true

  try {
    const formData = new FormData()
    formData.append("file", imageFile.value)
    formData.append("prompt", prompt.value)

    const res = await axios.post("http://localhost:8000/predict", formData)
    messages.value.push(res.data.markdown)

    // 清空输入，但保留图片以便重新识别
    prompt.value = ''

    ElMessage.success('识别完成！')
  } catch (error) {
    console.error('识别失败:', error)
    ElMessage.error('识别失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.chat-card {
  width: 800px;
  margin: 0 auto;
  min-height: 500px;
}

.chat-bubble {
  background: #f4f4f8;
  padding: 15px;
  margin: 15px 0;
  border-radius: 12px;
  border-left: 4px solid #409EFF;
}

.upload-box {
  margin: 20px 0;
}

.upload-placeholder {
  padding: 40px;
  text-align: center;
  color: #909399;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 12px;
  color: #C0C4CC;
}

.upload-success {
  padding: 20px;
  text-align: center;
  background: #f0f9ff;
  border: 2px dashed #409EFF;
  border-radius: 8px;
}

.success-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.success-text {
  color: #409EFF;
  margin-bottom: 8px;
}

.image-preview {
  position: relative;
  margin: 20px 0;
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.preview-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
}

.input-area {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.input-area .el-input {
  flex: 1;
}

.input-area .el-button {
  min-width: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-card {
    width: 95%;
    margin: 10px auto;
  }

  .input-area {
    flex-direction: column;
  }

  .input-area .el-button {
    width: 100%;
  }
}
</style>
