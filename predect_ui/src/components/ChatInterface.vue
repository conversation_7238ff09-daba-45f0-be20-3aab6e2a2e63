<template>
  <el-card class="chat-card" shadow="hover">
    <!-- 聊天消息区域 -->
    <div v-if="messages.length > 0">
      <div
        v-for="(msg, index) in messages"
        :key="index"
        class="chat-bubble"
        v-html="renderMarkdown(msg)"
      />
    </div>
    <div v-else class="empty-state">暂无对话，请上传图片并输入提示词</div>

    <!-- 图片上传区域 -->
    <el-upload
      class="upload-box"
      drag
      :auto-upload="false"
      :show-file-list="false"
      :before-upload="handleBeforeUpload"
    >
      <template #trigger>
        <el-icon class="upload-icon"><UploadFilled /></el-icon>
        <div class="upload-text">点击或拖拽上传图片</div>
        <div class="upload-hint">支持 JPG、PNG，最大 10MB</div>
      </template>
      <template #tip>
        <div v-if="imageFile" class="upload-success">
          ✅ 已选择图片：{{ imageFile.name }}
          <el-button type="danger" text size="small" @click.stop="clearImage">重新选择</el-button>
        </div>
      </template>
    </el-upload>

    <!-- 图片预览 -->
    <el-image
      v-if="imagePreview"
      :src="imagePreview"
      fit="contain"
      class="image-preview"
      :preview-src-list="[imagePreview]"
      preview-teleported
    >
      <template #error>
        <div>加载失败</div>
      </template>
    </el-image>

    <!-- 提示词输入 + 提交按钮 -->
    <div class="input-area">
      <el-input
        v-model="prompt"
        placeholder="请输入提示词，例如：请识别这个数学公式"
        :disabled="loading"
        clearable
        @keyup.enter="submitPrompt"
      />
      <el-button
        type="primary"
        :loading="loading"
        :disabled="!imageFile || !prompt.trim()"
        @click="submitPrompt"
      >
        {{ loading ? '识别中...' : '发送' }}
      </el-button>
    </div>
  </el-card>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import axios from 'axios'
import { marked } from 'marked'

const prompt = ref('')
const imageFile = ref(null)
const imagePreview = ref('')
const messages = ref([])
const loading = ref(false)

const renderMarkdown = (text) => {
  if (!text || typeof text !== 'string') return ''
  return marked.parse(text)
}

const handleBeforeUpload = (file) => {
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return false
  }
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过 10MB')
    return false
  }

  imageFile.value = file
  const reader = new FileReader()
  reader.onload = (e) => {
    imagePreview.value = e.target.result
  }
  reader.readAsDataURL(file)

  return false // 阻止自动上传
}

const clearImage = () => {
  imageFile.value = null
  imagePreview.value = ''
}

const submitPrompt = async () => {
  if (!imageFile.value || !prompt.value.trim()) return

  loading.value = true
  const formData = new FormData()
  formData.append('file', imageFile.value)
  formData.append('prompt', prompt.value)

  try {
    const res = await axios.post('http://localhost:8000/predict', formData)
    messages.value.push(res.data.markdown)
    prompt.value = ''
    ElMessage.success('识别完成！')
  } catch (err) {
    console.error(err)
    ElMessage.error('识别失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.chat-card {
  max-width: 800px;
  margin: 40px auto;
  padding: 24px;
}

.chat-bubble {
  background: #f9f9fb;
  padding: 16px;
  margin-bottom: 12px;
  border-left: 4px solid #409EFF;
  border-radius: 8px;
}

.empty-state {
  text-align: center;
  color: #bbb;
  font-size: 14px;
  padding: 40px 0;
}

.upload-box {
  margin: 20px 0;
  text-align: center;
}

.upload-icon {
  font-size: 40px;
  color: #409EFF;
  margin-bottom: 10px;
}

.upload-text {
  font-size: 16px;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 12px;
  color: #aaa;
}

.upload-success {
  margin-top: 10px;
  color: #409EFF;
  font-size: 14px;
}

.image-preview {
  width: 100%;
  max-height: 300px;
  object-fit: contain;
  margin: 12px 0;
  border-radius: 8px;
}

.input-area {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .input-area {
    flex-direction: column;
  }

  .input-area .el-button {
    width: 100%;
  }
}
</style>
