#!/usr/bin/env python3
"""
GPU 内存监控脚本
"""
import torch
import time
import psutil
import os

def format_bytes(bytes_value):
    """格式化字节数为可读格式"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.2f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.2f} PB"

def get_gpu_memory_info():
    """获取 GPU 内存信息"""
    if not torch.cuda.is_available():
        return "CUDA 不可用"
    
    device = torch.cuda.current_device()
    gpu_name = torch.cuda.get_device_name(device)
    
    # 获取内存信息
    total_memory = torch.cuda.get_device_properties(device).total_memory
    allocated_memory = torch.cuda.memory_allocated(device)
    cached_memory = torch.cuda.memory_reserved(device)
    free_memory = total_memory - cached_memory
    
    return {
        'gpu_name': gpu_name,
        'total': total_memory,
        'allocated': allocated_memory,
        'cached': cached_memory,
        'free': free_memory,
        'usage_percent': (cached_memory / total_memory) * 100
    }

def get_system_memory_info():
    """获取系统内存信息"""
    memory = psutil.virtual_memory()
    return {
        'total': memory.total,
        'available': memory.available,
        'used': memory.used,
        'usage_percent': memory.percent
    }

def print_memory_status():
    """打印内存状态"""
    print("=" * 60)
    print("GPU 和系统内存监控")
    print("=" * 60)
    
    # GPU 内存
    gpu_info = get_gpu_memory_info()
    if isinstance(gpu_info, str):
        print(f"GPU: {gpu_info}")
    else:
        print(f"GPU: {gpu_info['gpu_name']}")
        print(f"  总内存:   {format_bytes(gpu_info['total'])}")
        print(f"  已分配:   {format_bytes(gpu_info['allocated'])}")
        print(f"  已缓存:   {format_bytes(gpu_info['cached'])}")
        print(f"  可用:     {format_bytes(gpu_info['free'])}")
        print(f"  使用率:   {gpu_info['usage_percent']:.1f}%")
        
        # 内存使用状态
        if gpu_info['usage_percent'] > 90:
            print("  状态:     ⚠️  内存使用率过高")
        elif gpu_info['usage_percent'] > 70:
            print("  状态:     ⚡ 内存使用率较高")
        else:
            print("  状态:     ✅ 内存使用正常")
    
    print()
    
    # 系统内存
    sys_info = get_system_memory_info()
    print("系统内存:")
    print(f"  总内存:   {format_bytes(sys_info['total'])}")
    print(f"  已使用:   {format_bytes(sys_info['used'])}")
    print(f"  可用:     {format_bytes(sys_info['available'])}")
    print(f"  使用率:   {sys_info['usage_percent']:.1f}%")
    
    print()
    
    # 当前进程信息
    process = psutil.Process(os.getpid())
    process_memory = process.memory_info()
    print("当前进程:")
    print(f"  RSS:      {format_bytes(process_memory.rss)}")
    print(f"  VMS:      {format_bytes(process_memory.vms)}")
    
    print("=" * 60)

def clear_gpu_cache():
    """清理 GPU 缓存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("✅ GPU 缓存已清理")
    else:
        print("❌ CUDA 不可用，无法清理 GPU 缓存")

def monitor_continuous(interval=5):
    """连续监控内存使用"""
    print("开始连续监控 GPU 内存使用情况...")
    print("按 Ctrl+C 停止监控")
    print()
    
    try:
        while True:
            print_memory_status()
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "clear":
            clear_gpu_cache()
            print_memory_status()
        elif sys.argv[1] == "monitor":
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 5
            monitor_continuous(interval)
        else:
            print("用法:")
            print("  python monitor_gpu.py          # 显示当前内存状态")
            print("  python monitor_gpu.py clear    # 清理 GPU 缓存并显示状态")
            print("  python monitor_gpu.py monitor [间隔秒数]  # 连续监控")
    else:
        print_memory_status()
