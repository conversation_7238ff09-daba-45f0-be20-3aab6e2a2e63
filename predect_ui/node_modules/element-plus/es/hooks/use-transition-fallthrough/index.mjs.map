{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-transition-fallthrough/index.ts"], "sourcesContent": ["/* istanbul ignore file */\nimport { getCurrentInstance } from 'vue'\n\nconst AFTER_APPEAR = 'after-appear'\nconst AFTER_ENTER = 'after-enter'\nconst AFTER_LEAVE = 'after-leave'\nconst APPEAR = 'appear'\nconst APPEAR_CANCELLED = 'appear-cancelled'\nconst BEFORE_ENTER = 'before-enter'\nconst BEFORE_LEAVE = 'before-leave'\nconst ENTER = 'enter'\nconst ENTER_CANCELLED = 'enter-cancelled'\nconst LEAVE = 'leave'\nconst LEAVE_CANCELLED = 'leave-cancelled'\n\nexport const useTransitionFallthroughEmits = [\n  AFTER_APPEAR,\n  AFTER_ENTER,\n  AFTER_LEAVE,\n  APPEAR,\n  APPEAR_CANCELLED,\n  BEFORE_ENTER,\n  BEFORE_LEAVE,\n  ENTER,\n  ENTER_CANCELLED,\n  LEAVE,\n  LEAVE_CANCELLED,\n] as const\n\n// Sometimes we want to delegate the transition emitted event\n// we have to right the function locally, which is not a good\n// approach to this, so we created this hook for the event\n// fallthrough\n\n/**\n * NOTE:\n * This is only a delegator for delegating transition callbacks.\n * Use this at your need.\n */\n\n/**\n * Simple usage\n *\n * In your setups:\n *\n * setup() {\n *   const fallthroughMethods = useTransitionFallthrough()\n *   return fallthrough\n * }\n *\n * In your template:\n *\n * <template>\n *  <transition name=\"whatever\" v-bind=\"fallthrough\">\n *    <slot />\n *  </transition>\n * </template>\n *\n */\n\nexport const useTransitionFallthrough = () => {\n  const { emit } = getCurrentInstance()!\n\n  return {\n    onAfterAppear: () => {\n      emit(AFTER_APPEAR)\n    },\n    onAfterEnter: () => {\n      emit(AFTER_ENTER)\n    },\n    onAfterLeave: () => {\n      emit(AFTER_LEAVE)\n    },\n    onAppearCancelled: () => {\n      emit(APPEAR_CANCELLED)\n    },\n    onBeforeEnter: () => {\n      emit(BEFORE_ENTER)\n    },\n    onBeforeLeave: () => {\n      emit(BEFORE_LEAVE)\n    },\n    onEnter: () => {\n      emit(ENTER)\n    },\n    onEnterCancelled: () => {\n      emit(ENTER_CANCELLED)\n    },\n    onLeave: () => {\n      emit(LEAVE)\n    },\n    onLeaveCancelled: () => {\n      emit(LEAVE_CANCELLED)\n    },\n  }\n}\n"], "names": [], "mappings": ";;AACA,MAAM,YAAY,GAAG,cAAc,CAAC;AACpC,MAAM,WAAW,GAAG,aAAa,CAAC;AAClC,MAAM,WAAW,GAAG,aAAa,CAAC;AAClC,MAAM,MAAM,GAAG,QAAQ,CAAC;AACxB,MAAM,gBAAgB,GAAG,kBAAkB,CAAC;AAC5C,MAAM,YAAY,GAAG,cAAc,CAAC;AACpC,MAAM,YAAY,GAAG,cAAc,CAAC;AACpC,MAAM,KAAK,GAAG,OAAO,CAAC;AACtB,MAAM,eAAe,GAAG,iBAAiB,CAAC;AAC1C,MAAM,KAAK,GAAG,OAAO,CAAC;AACtB,MAAM,eAAe,GAAG,iBAAiB,CAAC;AAC9B,MAAC,6BAA6B,GAAG;AAC7C,EAAE,YAAY;AACd,EAAE,WAAW;AACb,EAAE,WAAW;AACb,EAAE,MAAM;AACR,EAAE,gBAAgB;AAClB,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,KAAK;AACP,EAAE,eAAe;AACjB,EAAE,KAAK;AACP,EAAE,eAAe;AACjB,EAAE;AACU,MAAC,wBAAwB,GAAG,MAAM;AAC9C,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,OAAO;AACT,IAAI,aAAa,EAAE,MAAM;AACzB,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,YAAY,EAAE,MAAM;AACxB,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,YAAY,EAAE,MAAM;AACxB,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,iBAAiB,EAAE,MAAM;AAC7B,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,aAAa,EAAE,MAAM;AACzB,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,aAAa,EAAE,MAAM;AACzB,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,KAAK;AACL,IAAI,gBAAgB,EAAE,MAAM;AAC5B,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC;AAC5B,KAAK;AACL,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,KAAK;AACL,IAAI,gBAAgB,EAAE,MAAM;AAC5B,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC;AAC5B,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}