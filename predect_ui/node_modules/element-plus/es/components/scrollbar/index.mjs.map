{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/scrollbar/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Scrollbar from './src/scrollbar.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElScrollbar: SFCWithInstall<typeof Scrollbar> =\n  withInstall(Scrollbar)\nexport default ElScrollbar\n\nexport * from './src/util'\nexport * from './src/scrollbar'\nexport * from './src/thumb'\nexport * from './src/constants'\n"], "names": [], "mappings": ";;;;;;;AAEY,MAAC,WAAW,GAAG,WAAW,CAAC,SAAS;;;;"}