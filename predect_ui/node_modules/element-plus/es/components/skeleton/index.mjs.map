{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/skeleton/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\nimport Skeleton from './src/skeleton.vue'\nimport SkeletonItem from './src/skeleton-item.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSkeleton: SFCWithInstall<typeof Skeleton> & {\n  SkeletonItem: typeof SkeletonItem\n} = withInstall(Skeleton, {\n  SkeletonItem,\n})\nexport const ElSkeletonItem: SFCWithInstall<typeof SkeletonItem> =\n  withNoopInstall(SkeletonItem)\nexport default ElSkeleton\n\nexport * from './src/skeleton'\nexport * from './src/skeleton-item'\n"], "names": [], "mappings": ";;;;;;AAGY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE;AAChD,EAAE,YAAY;AACd,CAAC,EAAE;AACS,MAAC,cAAc,GAAG,eAAe,CAAC,YAAY;;;;"}