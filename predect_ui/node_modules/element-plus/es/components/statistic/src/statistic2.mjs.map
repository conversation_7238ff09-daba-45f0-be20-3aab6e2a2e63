{"version": 3, "file": "statistic2.mjs", "sources": ["../../../../../../packages/components/statistic/src/statistic.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div v-if=\"$slots.title || title\" :class=\"ns.e('head')\">\n      <slot name=\"title\">\n        {{ title }}\n      </slot>\n    </div>\n    <div :class=\"ns.e('content')\">\n      <div v-if=\"$slots.prefix || prefix\" :class=\"ns.e('prefix')\">\n        <slot name=\"prefix\">\n          <span>{{ prefix }}</span>\n        </slot>\n      </div>\n      <span :class=\"ns.e('number')\" :style=\"valueStyle\">\n        {{ displayValue }}\n      </span>\n      <div v-if=\"$slots.suffix || suffix\" :class=\"ns.e('suffix')\">\n        <slot name=\"suffix\">\n          <span>{{ suffix }}</span>\n        </slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isFunction, isNumber } from '@element-plus/utils'\nimport { statisticProps } from './statistic'\n\ndefineOptions({\n  name: 'ElStatistic',\n})\n\nconst props = defineProps(statisticProps)\nconst ns = useNamespace('statistic')\n\nconst displayValue = computed(() => {\n  const { value, formatter, precision, decimalSeparator, groupSeparator } =\n    props\n\n  if (isFunction(formatter)) return formatter(value)\n\n  // https://github.com/element-plus/element-plus/issues/17784\n  if (!isNumber(value) || Number.isNaN(value)) return value\n\n  let [integer, decimal = ''] = String(value).split('.')\n  decimal = decimal\n    .padEnd(precision, '0')\n    .slice(0, precision > 0 ? precision : 0)\n  integer = integer.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator)\n  return [integer, decimal].join(decimal ? decimalSeparator : '')\n})\n\ndefineExpose({\n  /**\n   * @description current display value\n   */\n  displayValue,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;mCA+Bc,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAGA,IAAM,MAAA,EAAA,GAAK,aAAa,WAAW,CAAA,CAAA;AAEnC,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAClC,MAAA,MAAM,EAAE,KAAO,EAAA,SAAA,EAAW,SAAW,EAAA,gBAAA,EAAkB,gBACrD,GAAA,KAAA,CAAA;AAEF,MAAA,IAAI,UAAW,CAAA,SAAS,CAAG;AAG3B,QAAI,gBAAe,CAAA,KAAK;AAExB,MAAI,IAAA,CAAC,SAAS,KAAU,CAAA,IAAA,MAAM,CAAO,KAAA,CAAA,KAAK,CAAE;AAC5C,QAAU,OAAA,KAAA,CAAA;AAGV,MAAU,IAAA,CAAA,OAAA,EAAA,OAAgB,GAAA,EAAA,CAAA,GAAA,MAAA,CAAA,KAAA,CAAA,CAAA,KAAA,CAAA,GAAuC,CAAA,CAAA;AACjE,MAAA,OAAO,GAAU,OAAA,CAAA,MAAO,UAAO,EAAA,GAAA,CAAA,CAAU,qBAAqB,CAAA,GAAA,SAAA,GAAA,CAAA,CAAA,CAAA;AAAA,MAC/D,OAAA,GAAA,OAAA,CAAA,OAAA,CAAA,uBAAA,EAAA,cAAA,CAAA,CAAA;AAED,MAAa,OAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA,IAAA,CAAA,OAAA,GAAA,gBAAA,GAAA,EAAA,CAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,MAAA,CAAA;AAAA,MAAA,YAAA;AAAA,KAIX,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}