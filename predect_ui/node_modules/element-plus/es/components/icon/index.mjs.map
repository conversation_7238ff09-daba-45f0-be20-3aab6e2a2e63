{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/icon/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Icon from './src/icon.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElIcon: SFCWithInstall<typeof Icon> = withInstall(Icon)\nexport default ElIcon\n\nexport * from './src/icon'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,MAAM,GAAG,WAAW,CAAC,IAAI;;;;"}