{"version": 3, "file": "node.mjs", "sources": ["../../../../../../../packages/components/tree/src/model/node.ts"], "sourcesContent": ["import { reactive } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport {\n  hasOwn,\n  isArray,\n  isBoolean,\n  isFunction,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport { NODE_KEY, markNodeData } from './util'\n\nimport type TreeStore from './tree-store'\nimport type { Nullable } from '@element-plus/utils'\nimport type {\n  FakeNode,\n  TreeKey,\n  TreeNodeChildState,\n  TreeNodeData,\n  TreeNodeLoadedDefaultProps,\n  TreeNodeOptions,\n} from '../tree.type'\n\nexport const getChildState = (node: Node[]): TreeNodeChildState => {\n  let all = true\n  let none = true\n  let allWithoutDisable = true\n  for (let i = 0, j = node.length; i < j; i++) {\n    const n = node[i]\n    if (n.checked !== true || n.indeterminate) {\n      all = false\n      if (!n.disabled) {\n        allWithoutDisable = false\n      }\n    }\n    if (n.checked !== false || n.indeterminate) {\n      none = false\n    }\n  }\n\n  return { all, none, allWithoutDisable, half: !all && !none }\n}\n\nconst reInitChecked = function (node: Node): void {\n  if (node.childNodes.length === 0 || node.loading) return\n\n  const { all, none, half } = getChildState(node.childNodes)\n  if (all) {\n    node.checked = true\n    node.indeterminate = false\n  } else if (half) {\n    node.checked = false\n    node.indeterminate = true\n  } else if (none) {\n    node.checked = false\n    node.indeterminate = false\n  }\n\n  const parent = node.parent\n  if (!parent || parent.level === 0) return\n\n  if (!node.store.checkStrictly) {\n    reInitChecked(parent)\n  }\n}\n\nconst getPropertyFromData = function (node: Node, prop: string): any {\n  const props = node.store.props\n  const data = node.data || {}\n  const config = (props as any)[prop]\n\n  if (isFunction(config)) {\n    return config(data, node)\n  } else if (isString(config)) {\n    return data[config]\n  } else if (isUndefined(config)) {\n    const dataProp = data[prop]\n    return isUndefined(dataProp) ? '' : dataProp\n  }\n}\n\nlet nodeIdSeed = 0\n\nclass Node {\n  id: number\n  text: string | null\n  checked: boolean\n  indeterminate: boolean\n  data: TreeNodeData\n  expanded: boolean\n  parent: Node | null\n  visible: boolean\n  isCurrent: boolean\n  store!: TreeStore\n  isLeafByUser: boolean | undefined = undefined\n  isLeaf: boolean | undefined = undefined\n  canFocus: boolean\n\n  level: number\n  loaded: boolean\n  childNodes: Node[]\n  loading: boolean\n\n  constructor(options: TreeNodeOptions) {\n    this.id = nodeIdSeed++\n    this.text = null\n    this.checked = false\n    this.indeterminate = false\n    this.data = null as unknown as TreeNodeData\n    this.expanded = false\n    this.parent = null as Node | null\n    this.visible = true\n    this.isCurrent = false\n    this.canFocus = false\n\n    for (const name in options) {\n      if (hasOwn(options, name)) {\n        this[name] = options[name]\n      }\n    }\n\n    // internal\n    this.level = 0\n    this.loaded = false\n    this.childNodes = []\n    this.loading = false\n\n    if (this.parent) {\n      this.level = this.parent.level + 1\n    }\n  }\n\n  initialize() {\n    const store = this.store\n    if (!store) {\n      throw new Error('[Node]store is required!')\n    }\n    store.registerNode(this)\n\n    const props = store.props\n    if (props && typeof props.isLeaf !== 'undefined') {\n      const isLeaf = getPropertyFromData(this, 'isLeaf')\n      if (isBoolean(isLeaf)) {\n        this.isLeafByUser = isLeaf\n      }\n    }\n\n    if (store.lazy !== true && this.data) {\n      this.setData(this.data)\n\n      if (store.defaultExpandAll) {\n        this.expanded = true\n        this.canFocus = true\n      }\n    } else if (\n      this.level > 0 &&\n      store.lazy &&\n      store.defaultExpandAll &&\n      !this.isLeafByUser\n    ) {\n      this.expand()\n    }\n    if (!isArray(this.data)) {\n      markNodeData(this, this.data)\n    }\n    if (!this.data) return\n\n    const defaultExpandedKeys = store.defaultExpandedKeys\n    const key = store.key\n\n    if (\n      key &&\n      !isNil(this.key) &&\n      defaultExpandedKeys &&\n      defaultExpandedKeys.includes(this.key)\n    ) {\n      this.expand(null, store.autoExpandParent)\n    }\n\n    if (\n      key &&\n      store.currentNodeKey !== undefined &&\n      this.key === store.currentNodeKey\n    ) {\n      store.currentNode = this\n      store.currentNode.isCurrent = true\n    }\n\n    if (store.lazy) {\n      store._initDefaultCheckedNode(this)\n    }\n\n    this.updateLeafState()\n    if (this.level === 1 || this.parent?.expanded === true) this.canFocus = true\n  }\n\n  setData(data: TreeNodeData): void {\n    if (!isArray(data)) {\n      markNodeData(this, data)\n    }\n\n    this.data = data\n    this.childNodes = []\n\n    let children\n    if (this.level === 0 && isArray(this.data)) {\n      children = this.data\n    } else {\n      children = getPropertyFromData(this, 'children') || []\n    }\n\n    for (let i = 0, j = children.length; i < j; i++) {\n      this.insertChild({ data: children[i] })\n    }\n  }\n\n  get label(): string {\n    return getPropertyFromData(this, 'label')\n  }\n\n  get key(): TreeKey | null | undefined {\n    const nodeKey = this.store.key\n    if (this.data) return this.data[nodeKey]\n    return null\n  }\n\n  get disabled(): boolean {\n    return getPropertyFromData(this, 'disabled')\n  }\n\n  get nextSibling(): Nullable<Node> {\n    const parent = this.parent\n    if (parent) {\n      const index = parent.childNodes.indexOf(this)\n      if (index > -1) {\n        return parent.childNodes[index + 1]\n      }\n    }\n    return null\n  }\n\n  get previousSibling(): Nullable<Node> {\n    const parent = this.parent\n    if (parent) {\n      const index = parent.childNodes.indexOf(this)\n      if (index > -1) {\n        return index > 0 ? parent.childNodes[index - 1] : null\n      }\n    }\n    return null\n  }\n\n  contains(target: Node, deep = true): boolean {\n    return (this.childNodes || []).some(\n      (child) => child === target || (deep && child.contains(target))\n    )\n  }\n\n  remove(): void {\n    const parent = this.parent\n    if (parent) {\n      parent.removeChild(this)\n    }\n  }\n\n  insertChild(child?: FakeNode | Node, index?: number, batch?: boolean): void {\n    if (!child) throw new Error('InsertChild error: child is required.')\n\n    if (!(child instanceof Node)) {\n      if (!batch) {\n        const children = this.getChildren(true)\n        if (!children?.includes(child.data)) {\n          if (isUndefined(index) || index < 0) {\n            children?.push(child.data)\n          } else {\n            children?.splice(index, 0, child.data)\n          }\n        }\n      }\n      Object.assign(child, {\n        parent: this,\n        store: this.store,\n      })\n      child = reactive(new Node(child as TreeNodeOptions))\n      if (child instanceof Node) {\n        child.initialize()\n      }\n    }\n\n    ;(child as Node).level = this.level + 1\n\n    if (isUndefined(index) || index < 0) {\n      this.childNodes.push(child as Node)\n    } else {\n      this.childNodes.splice(index, 0, child as Node)\n    }\n\n    this.updateLeafState()\n  }\n\n  insertBefore(child: FakeNode | Node, ref: Node): void {\n    let index\n    if (ref) {\n      index = this.childNodes.indexOf(ref)\n    }\n    this.insertChild(child, index)\n  }\n\n  insertAfter(child: FakeNode | Node, ref: Node): void {\n    let index\n    if (ref) {\n      index = this.childNodes.indexOf(ref)\n      if (index !== -1) index += 1\n    }\n    this.insertChild(child, index)\n  }\n\n  removeChild(child: Node): void {\n    const children = this.getChildren() || []\n    const dataIndex = children.indexOf(child.data)\n    if (dataIndex > -1) {\n      children.splice(dataIndex, 1)\n    }\n\n    const index = this.childNodes.indexOf(child)\n\n    if (index > -1) {\n      this.store && this.store.deregisterNode(child)\n      child.parent = null\n      this.childNodes.splice(index, 1)\n    }\n\n    this.updateLeafState()\n  }\n\n  removeChildByData(data: TreeNodeData | null): void {\n    let targetNode: Node | null = null\n\n    for (let i = 0; i < this.childNodes.length; i++) {\n      if (this.childNodes[i].data === data) {\n        targetNode = this.childNodes[i]\n        break\n      }\n    }\n\n    if (targetNode) {\n      this.removeChild(targetNode)\n    }\n  }\n\n  expand(callback?: (() => void) | null, expandParent?: boolean): void {\n    const done = () => {\n      if (expandParent) {\n        let parent = this.parent\n        while (parent && parent.level > 0) {\n          parent.expanded = true\n          parent = parent.parent\n        }\n      }\n      this.expanded = true\n      if (callback) callback()\n      this.childNodes.forEach((item) => {\n        item.canFocus = true\n      })\n    }\n\n    if (this.shouldLoadData()) {\n      this.loadData((data) => {\n        if (isArray(data)) {\n          if (this.checked) {\n            this.setChecked(true, true)\n          } else if (!this.store.checkStrictly) {\n            reInitChecked(this)\n          }\n          done()\n        }\n      })\n    } else {\n      done()\n    }\n  }\n\n  doCreateChildren(\n    array: TreeNodeData[],\n    defaultProps: TreeNodeLoadedDefaultProps = {}\n  ): void {\n    array.forEach((item) => {\n      this.insertChild(\n        Object.assign({ data: item }, defaultProps),\n        undefined,\n        true\n      )\n    })\n  }\n\n  collapse(): void {\n    this.expanded = false\n    this.childNodes.forEach((item) => {\n      item.canFocus = false\n    })\n  }\n\n  shouldLoadData() {\n    return Boolean(this.store.lazy === true && this.store.load && !this.loaded)\n  }\n\n  updateLeafState(): void {\n    if (\n      this.store.lazy === true &&\n      this.loaded !== true &&\n      typeof this.isLeafByUser !== 'undefined'\n    ) {\n      this.isLeaf = this.isLeafByUser\n      return\n    }\n    const childNodes = this.childNodes\n    if (\n      !this.store.lazy ||\n      (this.store.lazy === true && this.loaded === true)\n    ) {\n      this.isLeaf = !childNodes || childNodes.length === 0\n      return\n    }\n    this.isLeaf = false\n  }\n\n  setChecked(\n    value?: boolean | string,\n    deep?: boolean,\n    recursion?: boolean,\n    passValue?: boolean\n  ) {\n    this.indeterminate = value === 'half'\n    this.checked = value === true\n\n    if (this.store.checkStrictly) return\n\n    if (!(this.shouldLoadData() && !this.store.checkDescendants)) {\n      const { all, allWithoutDisable } = getChildState(this.childNodes)\n\n      if (!this.isLeaf && !all && allWithoutDisable) {\n        this.checked = false\n        value = false\n      }\n\n      const handleDescendants = (): void => {\n        if (deep) {\n          const childNodes = this.childNodes\n          for (let i = 0, j = childNodes.length; i < j; i++) {\n            const child = childNodes[i]\n            passValue = passValue || value !== false\n            const isCheck = child.disabled ? child.checked : passValue\n            child.setChecked(isCheck, deep, true, passValue)\n          }\n          const { half, all } = getChildState(childNodes)\n          if (!all) {\n            this.checked = all\n            this.indeterminate = half\n          }\n        }\n      }\n\n      if (this.shouldLoadData()) {\n        // Only work on lazy load data.\n        this.loadData(\n          () => {\n            handleDescendants()\n            reInitChecked(this)\n          },\n          {\n            checked: value !== false,\n          }\n        )\n        return\n      } else {\n        handleDescendants()\n      }\n    }\n\n    const parent = this.parent\n    if (!parent || parent.level === 0) return\n\n    if (!recursion) {\n      reInitChecked(parent)\n    }\n  }\n\n  getChildren(forceInit = false): TreeNodeData | TreeNodeData[] | null {\n    // this is data\n    if (this.level === 0) return this.data\n    const data = this.data\n    if (!data) return null\n\n    const props = this.store.props\n    let children = 'children'\n    if (props) {\n      children = props.children || 'children'\n    }\n\n    if (isUndefined(data[children])) {\n      data[children] = null\n    }\n\n    if (forceInit && !data[children]) {\n      data[children] = []\n    }\n\n    return data[children]\n  }\n\n  updateChildren(): void {\n    const newData = (this.getChildren() || []) as TreeNodeData[]\n    const oldData = this.childNodes.map((node) => node.data)\n\n    const newDataMap: Record<TreeKey, TreeNodeData> = {}\n    const newNodes: TreeNodeData[] = []\n\n    newData.forEach((item, index) => {\n      const key = item[NODE_KEY]\n      const isNodeExists =\n        !!key && oldData.findIndex((data) => data?.[NODE_KEY] === key) >= 0\n      if (isNodeExists) {\n        newDataMap[key] = { index, data: item }\n      } else {\n        newNodes.push({ index, data: item })\n      }\n    })\n\n    if (!this.store.lazy) {\n      oldData.forEach((item) => {\n        if (!newDataMap[item?.[NODE_KEY]]) this.removeChildByData(item)\n      })\n    }\n\n    newNodes.forEach(({ index, data }) => {\n      this.insertChild({ data }, index)\n    })\n\n    this.updateLeafState()\n  }\n\n  loadData(\n    callback: (data?: TreeNodeData[]) => void,\n    defaultProps: TreeNodeLoadedDefaultProps = {}\n  ) {\n    if (\n      this.store.lazy === true &&\n      this.store.load &&\n      !this.loaded &&\n      (!this.loading || Object.keys(defaultProps).length)\n    ) {\n      this.loading = true\n\n      const resolve = (children: TreeNodeData[]) => {\n        this.childNodes = []\n\n        this.doCreateChildren(children, defaultProps)\n        this.loaded = true\n        this.loading = false\n\n        this.updateLeafState()\n        if (callback) {\n          callback.call(this, children)\n        }\n      }\n      const reject = () => {\n        this.loading = false\n      }\n\n      this.store.load(this, resolve, reject)\n    } else {\n      if (callback) {\n        callback.call(this)\n      }\n    }\n  }\n\n  eachNode(callback: (node: Node) => void) {\n    const arr: Node[] = [this]\n    while (arr.length) {\n      const node = arr.shift()!\n      arr.unshift(...node.childNodes)\n      callback(node)\n    }\n  }\n\n  reInitChecked() {\n    if (this.store.checkStrictly) return\n    reInitChecked(this)\n  }\n}\n\nexport default Node\n"], "names": [], "mappings": ";;;;;;AAWY,MAAC,aAAa,GAAG,CAAC,IAAI,KAAK;AACvC,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC;AACjB,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC;AAClB,EAAE,IAAI,iBAAiB,GAAG,IAAI,CAAC;AAC/B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC/C,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,CAAC,OAAO,KAAK,IAAI,IAAI,CAAC,CAAC,aAAa,EAAE;AAC/C,MAAM,GAAG,GAAG,KAAK,CAAC;AAClB,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE;AACvB,QAAQ,iBAAiB,GAAG,KAAK,CAAC;AAClC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,aAAa,EAAE;AAChD,MAAM,IAAI,GAAG,KAAK,CAAC;AACnB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AAC/D,EAAE;AACF,MAAM,aAAa,GAAG,SAAS,IAAI,EAAE;AACrC,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO;AAClD,IAAI,OAAO;AACX,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC7D,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,GAAG,MAAM,IAAI,IAAI,EAAE;AACnB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,GAAG,MAAM,IAAI,IAAI,EAAE;AACnB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B,EAAE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC;AACnC,IAAI,OAAO;AACX,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;AACjC,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;AAC1B,GAAG;AACH,CAAC,CAAC;AACF,MAAM,mBAAmB,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;AACjD,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AACjC,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;AAC/B,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE;AAC1B,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC9B,GAAG,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC/B,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;AACxB,GAAG,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;AAClC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC;AACjD,GAAG;AACH,CAAC,CAAC;AACF,IAAI,UAAU,GAAG,CAAC,CAAC;AACnB,MAAM,IAAI,CAAC;AACX,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,EAAE,GAAG,UAAU,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;AAChC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;AACjC,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACnC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AACzC,KAAK;AACL,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC7B,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC9B,IAAI,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW,EAAE;AACtD,MAAM,MAAM,MAAM,GAAG,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACzD,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;AAC7B,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;AACnC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;AAC1C,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,MAAM,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAClC,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,OAAO;AACP,KAAK,MAAM,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AAC7F,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC7B,MAAM,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;AAClB,MAAM,OAAO;AACb,IAAI,MAAM,mBAAmB,GAAG,KAAK,CAAC,mBAAmB,CAAC;AAC1D,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,mBAAmB,IAAI,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAClG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,IAAI,GAAG,IAAI,KAAK,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,cAAc,EAAE;AACrF,MAAM,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;AAC/B,MAAM,KAAK,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC;AACzC,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE;AACpB,MAAM,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,MAAM,IAAI;AACxF,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,CAAC,IAAI,EAAE;AAChB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACxB,MAAM,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,QAAQ,CAAC;AACjB,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AAC3B,KAAK,MAAM;AACX,MAAM,QAAQ,GAAG,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC;AAC7D,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG;AACH,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;AACnC,IAAI,IAAI,IAAI,CAAC,IAAI;AACjB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpD,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACtB,QAAQ,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC5C,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,eAAe,GAAG;AACxB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpD,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACtB,QAAQ,OAAO,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC/D,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,EAAE;AAChC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACvG,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,EAAE,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AACnC,IAAI,IAAI,CAAC,KAAK;AACd,MAAM,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AAC/D,IAAI,IAAI,EAAE,KAAK,YAAY,IAAI,CAAC,EAAE;AAClC,MAAM,IAAI,CAAC,KAAK,EAAE;AAClB,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAChD,QAAQ,IAAI,EAAE,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE;AAC1E,UAAU,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;AAC/C,YAAY,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAClE,WAAW,MAAM;AACjB,YAAY,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;AAC9E,WAAW;AACX,SAAS;AACT,OAAO;AACP,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;AAC3B,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,OAAO,CAAC,CAAC;AACT,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,MAAM,IAAI,KAAK,YAAY,IAAI,EAAE;AACjC,QAAQ,KAAK,CAAC,UAAU,EAAE,CAAC;AAC3B,OAAO;AACP,KAAK;AAEL,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACjC,IAAI,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;AACzC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAC9C,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE;AAC3B,IAAI,IAAI,KAAK,CAAC;AACd,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE;AAC1B,IAAI,IAAI,KAAK,CAAC;AACd,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC;AACtB,QAAQ,KAAK,IAAI,CAAC,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,WAAW,CAAC,KAAK,EAAE;AACrB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;AAC9C,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACnD,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,EAAE;AACxB,MAAM,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjD,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACpB,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACrD,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AAC1B,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,iBAAiB,CAAC,IAAI,EAAE;AAC1B,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC;AAC1B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AAC5C,QAAQ,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACxC,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AACnC,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,QAAQ,EAAE,YAAY,EAAE;AACjC,IAAI,MAAM,IAAI,GAAG,MAAM;AACvB,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACjC,QAAQ,OAAO,MAAM,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE;AAC3C,UAAU,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;AACjC,UAAU,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AACjC,SAAS;AACT,OAAO;AACP,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC3B,MAAM,IAAI,QAAQ;AAClB,QAAQ,QAAQ,EAAE,CAAC;AACnB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;AAC/B,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK;AAC9B,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;AAC3B,UAAU,IAAI,IAAI,CAAC,OAAO,EAAE;AAC5B,YAAY,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;AAChD,YAAY,aAAa,CAAC,IAAI,CAAC,CAAC;AAChC,WAAW;AACX,UAAU,IAAI,EAAE,CAAC;AACjB,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,IAAI,EAAE,CAAC;AACb,KAAK;AACL,GAAG;AACH,EAAE,gBAAgB,CAAC,KAAK,EAAE,YAAY,GAAG,EAAE,EAAE;AAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC5B,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,YAAY,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;AAClF,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACtC,MAAM,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC5B,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,cAAc,GAAG;AACnB,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChF,GAAG;AACH,EAAE,eAAe,GAAG;AACpB,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,WAAW,EAAE;AACtG,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;AACtC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACvC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;AAC9E,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;AAC3D,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB,GAAG;AACH,EAAE,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE;AAChD,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,MAAM,CAAC;AAC1C,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,IAAI,CAAC;AAClC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa;AAChC,MAAM,OAAO;AACb,IAAI,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;AAClE,MAAM,MAAM,EAAE,GAAG,EAAE,iBAAiB,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACxE,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,iBAAiB,EAAE;AACrD,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAC7B,QAAQ,KAAK,GAAG,KAAK,CAAC;AACtB,OAAO;AACP,MAAM,MAAM,iBAAiB,GAAG,MAAM;AACtC,QAAQ,IAAI,IAAI,EAAE;AAClB,UAAU,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC7C,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7D,YAAY,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACxC,YAAY,SAAS,GAAG,SAAS,IAAI,KAAK,KAAK,KAAK,CAAC;AACrD,YAAY,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,SAAS,CAAC;AACvE,YAAY,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AAC7D,WAAW;AACX,UAAU,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC;AAChE,UAAU,IAAI,CAAC,IAAI,EAAE;AACrB,YAAY,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAChC,YAAY,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AACtC,WAAW;AACX,SAAS;AACT,OAAO,CAAC;AACR,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;AACjC,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM;AAC5B,UAAU,iBAAiB,EAAE,CAAC;AAC9B,UAAU,aAAa,CAAC,IAAI,CAAC,CAAC;AAC9B,SAAS,EAAE;AACX,UAAU,OAAO,EAAE,KAAK,KAAK,KAAK;AAClC,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO;AACf,OAAO,MAAM;AACb,QAAQ,iBAAiB,EAAE,CAAC;AAC5B,OAAO;AACP,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC;AACrC,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC;AAC5B,KAAK;AACL,GAAG;AACH,EAAE,WAAW,CAAC,SAAS,GAAG,KAAK,EAAE;AACjC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC;AACxB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AACnC,IAAI,IAAI,QAAQ,GAAG,UAAU,CAAC;AAC9B,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,UAAU,CAAC;AAC9C,KAAK;AACL,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;AACrC,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AAC5B,KAAK;AACL,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACtC,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;AAC1B,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1B,GAAG;AACH,EAAE,cAAc,GAAG;AACnB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;AAC7C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7D,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC;AAC1B,IAAI,MAAM,QAAQ,GAAG,EAAE,CAAC;AACxB,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;AACrC,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;AACvH,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAChD,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;AAC1B,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAChC,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/D,UAAU,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACvC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK;AAC1C,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AACxC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,QAAQ,CAAC,QAAQ,EAAE,YAAY,GAAG,EAAE,EAAE;AACxC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAAE;AAC5H,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1B,MAAM,MAAM,OAAO,GAAG,CAAC,QAAQ,KAAK;AACpC,QAAQ,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AAC7B,QAAQ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACtD,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAC3B,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAC7B,QAAQ,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/B,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACxC,SAAS;AACT,OAAO,CAAC;AACR,MAAM,MAAM,MAAM,GAAG,MAAM;AAC3B,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAC7B,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC7C,KAAK,MAAM;AACX,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,CAAC,QAAQ,EAAE;AACrB,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACvB,IAAI,OAAO,GAAG,CAAC,MAAM,EAAE;AACvB,MAAM,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;AAC/B,MAAM,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;AACtC,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrB,KAAK;AACL,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa;AAChC,MAAM,OAAO;AACb,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;AACxB,GAAG;AACH;;;;"}