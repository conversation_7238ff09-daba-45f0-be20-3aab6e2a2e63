{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/input-number/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport InputNumber from './src/input-number.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElInputNumber: SFCWithInstall<typeof InputNumber> =\n  withInstall(InputNumber)\n\nexport default ElInputNumber\nexport * from './src/input-number'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,aAAa,GAAG,WAAW,CAAC,WAAW;;;;"}