{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-composition/index.ts"], "sourcesContent": ["import { nextTick, ref } from 'vue'\nimport { isKorean } from '@element-plus/utils'\n\ninterface UseCompositionOptions {\n  afterComposition: (event: CompositionEvent) => void\n  emit?: ((event: 'compositionstart', evt: CompositionEvent) => void) &\n    ((event: 'compositionupdate', evt: CompositionEvent) => void) &\n    ((event: 'compositionend', evt: CompositionEvent) => void)\n}\n\nexport function useComposition({\n  afterComposition,\n  emit,\n}: UseCompositionOptions) {\n  const isComposing = ref(false)\n\n  const handleCompositionStart = (event: CompositionEvent) => {\n    emit?.('compositionstart', event)\n    isComposing.value = true\n  }\n\n  const handleCompositionUpdate = (event: CompositionEvent) => {\n    emit?.('compositionupdate', event)\n    const text = (event.target as HTMLInputElement)?.value\n    const lastCharacter = text[text.length - 1] || ''\n    isComposing.value = !isKorean(lastCharacter)\n  }\n\n  const handleCompositionEnd = (event: CompositionEvent) => {\n    emit?.('compositionend', event)\n    if (isComposing.value) {\n      isComposing.value = false\n      nextTick(() => afterComposition(event))\n    }\n  }\n\n  const handleComposition = (event: CompositionEvent) => {\n    event.type === 'compositionend'\n      ? handleCompositionEnd(event)\n      : handleCompositionUpdate(event)\n  }\n\n  return {\n    isComposing,\n    handleComposition,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n  }\n}\n"], "names": ["ref", "isKorean", "nextTick"], "mappings": ";;;;;;;AAEO,SAAS,cAAc,CAAC;AAC/B,EAAE,gBAAgB;AAClB,EAAE,IAAI;AACN,CAAC,EAAE;AACH,EAAE,MAAM,WAAW,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AACjC,EAAE,MAAM,sBAAsB,GAAG,CAAC,KAAK,KAAK;AAC5C,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AAC5D,IAAI,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;AAC7B,GAAG,CAAC;AACJ,EAAE,MAAM,uBAAuB,GAAG,CAAC,KAAK,KAAK;AAC7C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAC7D,IAAI,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AACjE,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACtD,IAAI,WAAW,CAAC,KAAK,GAAG,CAACC,aAAQ,CAAC,aAAa,CAAC,CAAC;AACjD,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,CAAC,KAAK,KAAK;AAC1C,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AAC1D,IAAI,IAAI,WAAW,CAAC,KAAK,EAAE;AAC3B,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAChC,MAAMC,YAAQ,CAAC,MAAM,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,KAAK,KAAK;AACvC,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB,GAAG,oBAAoB,CAAC,KAAK,CAAC,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC;AACnG,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAC3B,IAAI,oBAAoB;AACxB,GAAG,CAAC;AACJ;;;;"}