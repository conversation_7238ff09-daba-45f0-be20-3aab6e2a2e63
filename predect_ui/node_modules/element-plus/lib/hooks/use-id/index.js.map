{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-id/index.ts"], "sourcesContent": ["import { getCurrentInstance, inject, unref } from 'vue'\nimport { type MaybeRef, computedEager } from '@vueuse/core'\nimport { debugWarn, isClient } from '@element-plus/utils'\nimport { useGetDerivedNamespace } from '../use-namespace'\n\nimport type { InjectionKey, Ref } from 'vue'\n\nexport type ElIdInjectionContext = {\n  prefix: number\n  current: number\n}\n\nconst defaultIdInjection = {\n  prefix: Math.floor(Math.random() * 10000),\n  current: 0,\n}\n\nexport const ID_INJECTION_KEY: InjectionKey<ElIdInjectionContext> =\n  Symbol('elIdInjection')\n\nexport const useIdInjection = (): ElIdInjectionContext => {\n  return getCurrentInstance()\n    ? inject(ID_INJECTION_KEY, defaultIdInjection)\n    : defaultIdInjection\n}\n\nexport const useId = (deterministicId?: MaybeRef<string>): Ref<string> => {\n  const idInjection = useIdInjection()\n  if (!isClient && idInjection === defaultIdInjection) {\n    debugWarn(\n      'IdInjection',\n      `Looks like you are using server rendering, you must provide a id provider to ensure the hydration process to be succeed\nusage: app.provide(ID_INJECTION_KEY, {\n  prefix: number,\n  current: number,\n})`\n    )\n  }\n\n  const namespace = useGetDerivedNamespace()\n\n  // NOTE: Here we use `computedEager` to calculate the id value immediately, avoiding inconsistent id generation due to the lazy feature of `computed` when server rendering.\n  const idRef = computedEager(\n    () =>\n      unref(deterministicId) ||\n      `${namespace.value}-id-${idInjection.prefix}-${idInjection.current++}`\n  )\n\n  return idRef\n}\n"], "names": ["getCurrentInstance", "inject", "isClient", "debugWarn", "useGetDerivedNamespace", "computedEager", "unref"], "mappings": ";;;;;;;;;AAIA,MAAM,kBAAkB,GAAG;AAC3B,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;AACzC,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AACU,MAAC,gBAAgB,GAAG,MAAM,CAAC,eAAe,EAAE;AAC5C,MAAC,cAAc,GAAG,MAAM;AACpC,EAAE,OAAOA,sBAAkB,EAAE,GAAGC,UAAM,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,GAAG,kBAAkB,CAAC;AAClG,EAAE;AACU,MAAC,KAAK,GAAG,CAAC,eAAe,KAAK;AAC1C,EAAE,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AACvC,EAAE,IAAI,CAACC,aAAQ,IAAI,WAAW,KAAK,kBAAkB,EAAE;AACvD,IAAIC,eAAS,CAAC,aAAa,EAAE,CAAC;AAC9B;AACA;AACA;AACA,EAAE,CAAC,CAAC,CAAC;AACL,GAAG;AACH,EAAE,MAAM,SAAS,GAAGC,4BAAsB,EAAE,CAAC;AAC7C,EAAE,MAAM,KAAK,GAAGC,kBAAa,CAAC,MAAMC,SAAK,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;AACtI,EAAE,OAAO,KAAK,CAAC;AACf;;;;;;"}