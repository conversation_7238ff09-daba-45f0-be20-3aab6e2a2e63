{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-popper-container/index.ts"], "sourcesContent": ["import { computed, onBeforeMount } from 'vue'\nimport { isClient } from '@element-plus/utils'\nimport { useGetDerivedNamespace } from '../use-namespace'\nimport { useIdInjection } from '../use-id'\n\nexport const usePopperContainerId = () => {\n  const namespace = useGetDerivedNamespace()\n  const idInjection = useIdInjection()\n\n  const id = computed(() => {\n    return `${namespace.value}-popper-container-${idInjection.prefix}`\n  })\n  const selector = computed(() => `#${id.value}`)\n\n  return {\n    id,\n    selector,\n  }\n}\n\nconst createContainer = (id: string) => {\n  const container = document.createElement('div')\n  container.id = id\n  document.body.appendChild(container)\n  return container\n}\n\nexport const usePopperContainer = () => {\n  const { id, selector } = usePopperContainerId()\n  onBeforeMount(() => {\n    if (!isClient) return\n\n    // This is for bypassing the error that when under testing env, we often encounter\n    // document.body.innerHTML = '' situation\n    // for this we need to disable the caching since it's not really needed\n    if (\n      process.env.NODE_ENV === 'test' ||\n      !document.body.querySelector(selector.value)\n    ) {\n      createContainer(id.value)\n    }\n  })\n\n  return {\n    id,\n    selector,\n  }\n}\n"], "names": ["useGetDerivedNamespace", "useIdInjection", "computed", "onBeforeMount", "isClient"], "mappings": ";;;;;;;;;AAIY,MAAC,oBAAoB,GAAG,MAAM;AAC1C,EAAE,MAAM,SAAS,GAAGA,4BAAsB,EAAE,CAAC;AAC7C,EAAE,MAAM,WAAW,GAAGC,sBAAc,EAAE,CAAC;AACvC,EAAE,MAAM,EAAE,GAAGC,YAAQ,CAAC,MAAM;AAC5B,IAAI,OAAO,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,kBAAkB,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;AACvE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAGA,YAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClD,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,QAAQ;AACZ,GAAG,CAAC;AACJ,EAAE;AACF,MAAM,eAAe,GAAG,CAAC,EAAE,KAAK;AAChC,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAClD,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC;AACpB,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACvC,EAAE,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AACU,MAAC,kBAAkB,GAAG,MAAM;AACxC,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,oBAAoB,EAAE,CAAC;AAClD,EAAEC,iBAAa,CAAC,MAAM;AACtB,IAAI,IAAI,CAACC,aAAQ;AACjB,MAAM,OAAO;AACb,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACzF,MAAM,eAAe,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,QAAQ;AACZ,GAAG,CAAC;AACJ;;;;;"}