{"version": 3, "file": "size.js", "sources": ["../../../../packages/constants/size.ts"], "sourcesContent": ["export const componentSizes = ['', 'default', 'small', 'large'] as const\n\nexport type ComponentSize = typeof componentSizes[number]\n\nexport const componentSizeMap = {\n  large: 40,\n  default: 32,\n  small: 24,\n} as const\n"], "names": [], "mappings": ";;;;AAAY,MAAC,cAAc,GAAG,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE;AACpD,MAAC,gBAAgB,GAAG;AAChC,EAAE,KAAK,EAAE,EAAE;AACX,EAAE,OAAO,EAAE,EAAE;AACb,EAAE,KAAK,EAAE,EAAE;AACX;;;;;"}