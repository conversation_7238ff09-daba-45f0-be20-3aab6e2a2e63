{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/popconfirm/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Popconfirm from './src/popconfirm.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElPopconfirm: SFCWithInstall<typeof Popconfirm> =\n  withInstall(Popconfirm)\nexport default ElPopconfirm\n\nexport * from './src/popconfirm'\n"], "names": ["withInstall", "Popconfirm"], "mappings": ";;;;;;;;AAEY,MAAC,YAAY,GAAGA,mBAAW,CAACC,uBAAU;;;;;;;"}