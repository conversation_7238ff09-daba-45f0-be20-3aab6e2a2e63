{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/drawer/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Drawer from './src/drawer.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDrawer: SFCWithInstall<typeof Drawer> = withInstall(Drawer)\nexport default ElDrawer\n\nexport * from './src/drawer'\n"], "names": ["withInstall", "Drawer"], "mappings": ";;;;;;;;AAEY,MAAC,QAAQ,GAAGA,mBAAW,CAACC,mBAAM;;;;;;;"}