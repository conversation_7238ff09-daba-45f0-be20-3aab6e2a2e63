{"version": 3, "file": "split-bar.js", "sources": ["../../../../../../packages/components/splitter/src/split-bar.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { computed, ref } from 'vue'\nimport {\n  ArrowDown,\n  ArrowLeft,\n  ArrowRight,\n  ArrowUp,\n} from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nconst ns = useNamespace('splitter-bar')\n\ndefineOptions({\n  name: 'ElSplitterBar',\n})\n\nconst props = defineProps({\n  index: {\n    type: Number,\n    required: true,\n  },\n  layout: {\n    type: String,\n    values: ['horizontal', 'vertical'] as const,\n    default: 'horizontal',\n  },\n  resizable: {\n    type: Boolean,\n    default: true,\n  },\n  startCollapsible: {\n    type: Boolean,\n  },\n  endCollapsible: {\n    type: <PERSON><PERSON>an,\n  },\n})\n\nconst emit = defineEmits(['moveStart', 'moving', 'moveEnd', 'collapse'])\n\nconst isHorizontal = computed(() => props.layout === 'horizontal')\n\nconst barWrapStyles = computed(() => {\n  if (isHorizontal.value) {\n    return { width: 0 }\n  }\n  return { height: 0 }\n})\n\nconst draggerStyles = computed(() => {\n  return {\n    width: isHorizontal.value ? '16px' : '100%',\n    height: isHorizontal.value ? '100%' : '16px',\n    cursor: isHorizontal.value ? 'col-resize' : 'row-resize',\n    touchAction: 'none',\n  }\n})\n\nconst draggerPseudoClass = computed(() => {\n  const prefix = ns.e('dragger')\n  let className = isHorizontal.value\n    ? `${prefix}-horizontal`\n    : `${prefix}-vertical`\n  if (startPos.value) className += ` ${prefix}-active`\n  return className\n})\n\nconst startPos = ref<[x: number, y: number] | null>(null)\n\n// Start dragging\nconst onMousedown = (e: MouseEvent) => {\n  if (!props.resizable) return\n  startPos.value = [e.pageX, e.pageY]\n  emit('moveStart', props.index)\n  window.addEventListener('mouseup', onMouseUp)\n  window.addEventListener('mousemove', onMouseMove)\n}\n\nconst onTouchStart = (e: TouchEvent) => {\n  if (props.resizable && e.touches.length === 1) {\n    e.preventDefault()\n    const touch = e.touches[0]\n    startPos.value = [touch.pageX, touch.pageY]\n    emit('moveStart', props.index)\n    window.addEventListener('touchend', onTouchEnd)\n    window.addEventListener('touchmove', onTouchMove)\n  }\n}\n\n// During dragging\nconst onMouseMove = (e: MouseEvent) => {\n  const { pageX, pageY } = e\n  const offsetX = pageX - startPos.value![0]\n  const offsetY = pageY - startPos.value![1]\n  const offset = isHorizontal.value ? offsetX : offsetY\n  emit('moving', props.index, offset)\n}\n\nconst onTouchMove = (e: TouchEvent) => {\n  if (e.touches.length === 1) {\n    e.preventDefault()\n    const touch = e.touches[0]\n    const offsetX = touch.pageX - startPos.value![0]\n    const offsetY = touch.pageY - startPos.value![1]\n    const offset = isHorizontal.value ? offsetX : offsetY\n    emit('moving', props.index, offset)\n  }\n}\n\n// End dragging\nconst onMouseUp = () => {\n  startPos.value = null\n  window.removeEventListener('mouseup', onMouseUp)\n  window.removeEventListener('mousemove', onMouseMove)\n  emit('moveEnd', props.index)\n}\n\nconst onTouchEnd = () => {\n  startPos.value = null\n  window.removeEventListener('touchend', onTouchEnd)\n  window.removeEventListener('touchmove', onTouchMove)\n  emit('moveEnd', props.index)\n}\n\nconst StartIcon = computed(() => (isHorizontal.value ? ArrowLeft : ArrowUp))\nconst EndIcon = computed(() => (isHorizontal.value ? ArrowRight : ArrowDown))\n</script>\n\n<template>\n  <div :class=\"[ns.b()]\" :style=\"barWrapStyles\">\n    <div\n      v-if=\"startCollapsible\"\n      :class=\"[ns.e('collapse-icon'), ns.e(`${layout}-collapse-icon-start`)]\"\n      @click=\"emit('collapse', index, 'start')\"\n    >\n      <slot name=\"start-collapsible\">\n        <component :is=\"StartIcon\" style=\"width: 12px; height: 12px\" />\n      </slot>\n    </div>\n\n    <div\n      :class=\"[\n        ns.e('dragger'),\n        draggerPseudoClass,\n        resizable ? '' : ns.e('disable'),\n      ]\"\n      :style=\"draggerStyles\"\n      @mousedown=\"onMousedown\"\n      @touchstart=\"onTouchStart\"\n    />\n    <div\n      v-if=\"endCollapsible\"\n      :class=\"[ns.e('collapse-icon'), ns.e(`${layout}-collapse-icon-end`)]\"\n      @click=\"emit('collapse', index, 'end')\"\n    >\n      <slot name=\"end-collapsible\">\n        <component :is=\"EndIcon\" style=\"width: 12px; height: 12px\" />\n      </slot>\n    </div>\n  </div>\n</template>\n"], "names": ["useNamespace", "computed", "ref"], "mappings": ";;;;;;;;;uCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJA,IAAM,MAAA,EAAA,GAAKA,mBAAa,cAAc,CAAA,CAAA;AA8BtC,IAAA,MAAM,YAAe,GAAAC,YAAA,CAAS,MAAM,KAAA,CAAM,WAAW,YAAY,CAAA,CAAA;AAEjE,IAAM,MAAA,aAAA,GAAgBA,aAAS,MAAM;AACnC,MAAA,IAAI,aAAa,KAAO,EAAA;AACtB,QAAO,OAAA,EAAE,OAAO,CAAE,EAAA,CAAA;AAAA,OACpB;AACA,MAAO,OAAA,EAAE,QAAQ,CAAE,EAAA,CAAA;AAAA,KACpB,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgBA,aAAS,MAAM;AACnC,MAAO,OAAA;AAAA,QACL,KAAA,EAAO,YAAa,CAAA,KAAA,GAAQ,MAAS,GAAA,MAAA;AAAA,QACrC,MAAA,EAAQ,YAAa,CAAA,KAAA,GAAQ,MAAS,GAAA,MAAA;AAAA,QACtC,MAAA,EAAQ,YAAa,CAAA,KAAA,GAAQ,YAAe,GAAA,YAAA;AAAA,QAC5C,WAAa,EAAA,MAAA;AAAA,OACf,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,kBAAA,GAAqBA,aAAS,MAAM;AACxC,MAAM,MAAA,MAAA,GAAS,EAAG,CAAA,CAAA,CAAE,SAAS,CAAA,CAAA;AAC7B,MAAA,IAAI,YAAY,YAAa,CAAA,KAAA,GACzB,GAAG,MAAM,CAAA,WAAA,CAAA,GACT,GAAG,MAAM,CAAA,SAAA,CAAA,CAAA;AACb,MAAA,IAAI,QAAS,CAAA,KAAA;AACb,QAAO,SAAA,IAAA,CAAA,CAAA,EAAA,MAAA,CAAA,OAAA,CAAA,CAAA;AAAA,MACR,OAAA,SAAA,CAAA;AAED,KAAM,CAAA,CAAA;AAGN,IAAM,MAAA,QAAA,GAAAC,OAAc,CAAC,IAAkB,CAAA,CAAA;AACrC,IAAI,MAAA,WAAkB,GAAA,CAAA,CAAA,KAAA;AACtB,MAAA,IAAA,CAAA,KAAiB,CAAA,SAAG;AACpB,QAAK,OAAA;AACL,MAAO,QAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA,eAA4B,CAAS;AAC5C,MAAO,IAAA,CAAA,WAAA,EAAA,KAAA,CAAiB;AAAwB,MAClD,MAAA,CAAA,gBAAA,CAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AAEA,MAAM,MAAA,CAAA,gBAAkC,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA;AACtC,KAAA,CAAA;AACE,IAAA,MAAE,YAAe,GAAA,CAAA,CAAA,KAAA;AACjB,MAAM,IAAA,KAAA,CAAA,SAAU,IAAA,CAAA,CAAA,OAAS,CAAA,MAAA,KAAA,CAAA,EAAA;AACzB,QAAA,CAAA,CAAA,cAAiB,EAAC,CAAM;AACxB,QAAK,MAAA,KAAA,GAAA,CAAA,CAAA,QAAmB,CAAK,CAAA,CAAA;AAC7B,QAAO,QAAA,CAAA,KAAA,GAAA,CAAA,KAAA,CAAiB,YAAY,CAAU,KAAA,CAAA,CAAA;AAC9C,QAAO,IAAA,CAAA,WAAA,EAAA,KAAA,CAAiB;AAAwB,QAClD,MAAA,CAAA,gBAAA,CAAA,UAAA,EAAA,UAAA,CAAA,CAAA;AAAA,QACF,MAAA,CAAA,gBAAA,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA;AAGA,OAAM;AACJ,KAAM,CAAA;AACN,IAAA,MAAA,WAAgB,GAAA,CAAA,CAAA,KAAiB;AACjC,MAAA,MAAM,EAAU,KAAA,EAAA,KAAA,EAAA,GAAiB,CAAA,CAAA;AACjC,MAAM,MAAA,OAAA,GAAsB,KAAA,GAAA,QAAA,CAAA,KAAkB,CAAA,CAAA,CAAA,CAAA;AAC9C,MAAK,MAAA,OAAA,GAAgB,KAAA,GAAA,QAAa,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACpC,MAAA,MAAA,GAAA,YAAA,CAAA,KAAA,GAAA,OAAA,GAAA,OAAA,CAAA;AAEA,MAAM,IAAA,CAAA,QAAA,EAAA,KAAiC,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA;AACrC,KAAI,CAAA;AACF,IAAA,MAAE,WAAe,GAAA,CAAA,CAAA,KAAA;AACjB,MAAM,IAAA,CAAA,CAAA,OAAA,CAAA,MAAU,KAAA,CAAQ,EAAC;AACzB,QAAA,CAAA,CAAA,cAAgB,EAAA,CAAA;AAChB,QAAA,MAAM,KAAU,GAAA,CAAA,CAAA,OAAc,CAAA,CAAA,CAAA,CAAA;AAC9B,QAAM,MAAA,OAAA,GAAsB,KAAA,CAAA,KAAA,GAAA,QAAkB,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAC9C,QAAK,MAAA,OAAA,GAAgB,KAAA,CAAA,KAAA,GAAa,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QACpC,MAAA,MAAA,GAAA,YAAA,CAAA,KAAA,GAAA,OAAA,GAAA,OAAA,CAAA;AAAA,QACF,IAAA,CAAA,QAAA,EAAA,KAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA;AAGA,OAAA;AACE,KAAA,CAAA;AACA,IAAO,MAAA,SAAA,GAAA,MAAA;AACP,MAAO,QAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACP,MAAK,MAAA,CAAA,mBAAsB,CAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AAAA,MAC7B,MAAA,CAAA,mBAAA,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA;AAEA,MAAA,IAAM,iBAAmB,CAAA,KAAA,CAAA,CAAA;AACvB,KAAA,CAAA;AACA,IAAO,MAAA,UAAA,GAAA,MAAA;AACP,MAAO,QAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACP,MAAK,MAAA,CAAA,mBAAsB,CAAA,UAAA,EAAA,UAAA,CAAA,CAAA;AAAA,MAC7B,MAAA,CAAA,mBAAA,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA;AAEA,MAAA,IAAM,YAAY,KAAS,CAAA,KAAA,CAAA,CAAA;AAC3B,KAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}