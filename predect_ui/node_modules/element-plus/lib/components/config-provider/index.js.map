{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/config-provider/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport ConfigProvider from './src/config-provider'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElConfigProvider: SFCWithInstall<typeof ConfigProvider> =\n  withInstall(ConfigProvider)\nexport default ElConfigProvider\n\nexport * from './src/config-provider'\nexport * from './src/config-provider-props'\nexport * from './src/constants'\nexport * from './src/hooks/use-global-config'\n"], "names": ["withInstall", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAEY,MAAC,gBAAgB,GAAGA,mBAAW,CAACC,yBAAc;;;;;;;;;;;"}