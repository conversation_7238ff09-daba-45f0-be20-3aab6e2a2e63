{"version": 3, "file": "message.js", "sources": ["../../../../../../packages/components/message/src/message.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  iconPropType,\n  isClient,\n  mutable,\n} from '@element-plus/utils'\n\nimport type {\n  AppContext,\n  ExtractPropTypes,\n  VNode,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type { Mutable } from '@element-plus/utils'\nimport type MessageConstructor from './message.vue'\n\nexport const messageTypes = [\n  'primary',\n  'success',\n  'info',\n  'warning',\n  'error',\n] as const\n\nexport type messageType = typeof messageTypes[number]\n\nexport interface MessageConfigContext {\n  max?: number\n  grouping?: boolean\n  duration?: number\n  offset?: number\n  showClose?: boolean\n  plain?: boolean\n}\n\nexport const messageDefaults = mutable({\n  customClass: '',\n  dangerouslyUseHTMLString: false,\n  duration: 3000,\n  icon: undefined,\n  id: '',\n  message: '',\n  onClose: undefined,\n  showClose: false,\n  type: 'info',\n  plain: false,\n  offset: 16,\n  zIndex: 0,\n  grouping: false,\n  repeatNum: 1,\n  appendTo: isClient ? document.body : (undefined as never),\n} as const)\n\nexport const messageProps = buildProps({\n  /**\n   * @description custom class name for Message\n   */\n  customClass: {\n    type: String,\n    default: messageDefaults.customClass,\n  },\n  /**\n   * @description whether `message` is treated as HTML string\n   */\n  dangerouslyUseHTMLString: {\n    type: Boolean,\n    default: messageDefaults.dangerouslyUseHTMLString,\n  },\n  /**\n   * @description display duration, millisecond. If set to 0, it will not turn off automatically\n   */\n  duration: {\n    type: Number,\n    default: messageDefaults.duration,\n  },\n  /**\n   * @description custom icon component, overrides `type`\n   */\n  icon: {\n    type: iconPropType,\n    default: messageDefaults.icon,\n  },\n  /**\n   * @description message dom id\n   */\n  id: {\n    type: String,\n    default: messageDefaults.id,\n  },\n  /**\n   * @description message text\n   */\n  message: {\n    type: definePropType<string | VNode | (() => VNode)>([\n      String,\n      Object,\n      Function,\n    ]),\n    default: messageDefaults.message,\n  },\n  /**\n   * @description callback function when closed with the message instance as the parameter\n   */\n  onClose: {\n    type: definePropType<() => void>(Function),\n    default: messageDefaults.onClose,\n  },\n  /**\n   * @description whether to show a close button\n   */\n  showClose: {\n    type: Boolean,\n    default: messageDefaults.showClose,\n  },\n  /**\n   * @description message type\n   */\n  type: {\n    type: String,\n    values: messageTypes,\n    default: messageDefaults.type,\n  },\n  /**\n   * @description whether message is plain\n   */\n  plain: {\n    type: Boolean,\n    default: messageDefaults.plain,\n  },\n  /**\n   * @description set the distance to the top of viewport\n   */\n  offset: {\n    type: Number,\n    default: messageDefaults.offset,\n  },\n  /**\n   * @description input box size\n   */\n  zIndex: {\n    type: Number,\n    default: messageDefaults.zIndex,\n  },\n  /**\n   * @description merge messages with the same content, type of VNode message is not supported\n   */\n  grouping: {\n    type: Boolean,\n    default: messageDefaults.grouping,\n  },\n  /**\n   * @description The number of repetitions, similar to badge, is used as the initial number when used with `grouping`\n   */\n  repeatNum: {\n    type: Number,\n    default: messageDefaults.repeatNum,\n  },\n} as const)\nexport type MessageProps = ExtractPropTypes<typeof messageProps>\nexport type MessagePropsPublic = __ExtractPublicPropTypes<typeof messageProps>\n\nexport const messageEmits = {\n  destroy: () => true,\n}\nexport type MessageEmits = typeof messageEmits\n\nexport type MessageInstance = InstanceType<typeof MessageConstructor> & unknown\n\nexport type MessageOptions = Partial<\n  Mutable<\n    Omit<MessageProps, 'id'> & {\n      appendTo?: HTMLElement | string\n    }\n  >\n>\nexport type MessageParams = MessageOptions | MessageOptions['message']\nexport type MessageParamsNormalized = Omit<MessageProps, 'id'> & {\n  /**\n   * @description set the root element for the message, default to `document.body`\n   */\n  appendTo: HTMLElement\n}\nexport type MessageOptionsWithType = Omit<MessageOptions, 'type'>\nexport type MessageParamsWithType =\n  | MessageOptionsWithType\n  | MessageOptions['message']\n\nexport interface MessageHandler {\n  /**\n   * @description close the Message\n   */\n  close: () => void\n}\n\nexport type MessageFn = {\n  (options?: MessageParams, appContext?: null | AppContext): MessageHandler\n  closeAll(type?: messageType): void\n}\nexport type MessageTypedFn = (\n  options?: MessageParamsWithType,\n  appContext?: null | AppContext\n) => MessageHandler\n\nexport type Message = MessageFn & {\n  primary: MessageTypedFn\n  success: MessageTypedFn\n  warning: MessageTypedFn\n  info: MessageTypedFn\n  error: MessageTypedFn\n}\n"], "names": ["mutable", "isClient", "buildProps", "iconPropType", "definePropType"], "mappings": ";;;;;;;;;AAOY,MAAC,YAAY,GAAG;AAC5B,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,MAAM;AACR,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE;AACU,MAAC,eAAe,GAAGA,kBAAO,CAAC;AACvC,EAAE,WAAW,EAAE,EAAE;AACjB,EAAE,wBAAwB,EAAE,KAAK;AACjC,EAAE,QAAQ,EAAE,GAAG;AACf,EAAE,IAAI,EAAE,KAAK,CAAC;AACd,EAAE,EAAE,EAAE,EAAE;AACR,EAAE,OAAO,EAAE,EAAE;AACb,EAAE,OAAO,EAAE,KAAK,CAAC;AACjB,EAAE,SAAS,EAAE,KAAK;AAClB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,MAAM,EAAE,EAAE;AACZ,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,QAAQ,EAAE,KAAK;AACjB,EAAE,SAAS,EAAE,CAAC;AACd,EAAE,QAAQ,EAAEC,aAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC;AAC7C,CAAC,EAAE;AACS,MAAC,YAAY,GAAGC,kBAAU,CAAC;AACvC,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,eAAe,CAAC,WAAW;AACxC,GAAG;AACH,EAAE,wBAAwB,EAAE;AAC5B,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,eAAe,CAAC,wBAAwB;AACrD,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,eAAe,CAAC,QAAQ;AACrC,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,iBAAY;AACtB,IAAI,OAAO,EAAE,eAAe,CAAC,IAAI;AACjC,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,eAAe,CAAC,EAAE;AAC/B,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEC,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,IAAI,OAAO,EAAE,eAAe,CAAC,OAAO;AACpC,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,eAAe,CAAC,OAAO;AACpC,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,eAAe,CAAC,SAAS;AACtC,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,YAAY;AACxB,IAAI,OAAO,EAAE,eAAe,CAAC,IAAI;AACjC,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,eAAe,CAAC,KAAK;AAClC,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,eAAe,CAAC,MAAM;AACnC,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,eAAe,CAAC,MAAM;AACnC,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,eAAe,CAAC,QAAQ;AACrC,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,eAAe,CAAC,SAAS;AACtC,GAAG;AACH,CAAC,EAAE;AACS,MAAC,YAAY,GAAG;AAC5B,EAAE,OAAO,EAAE,MAAM,IAAI;AACrB;;;;;;;"}