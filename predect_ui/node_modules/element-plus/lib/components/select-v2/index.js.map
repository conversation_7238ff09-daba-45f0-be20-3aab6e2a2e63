{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/select-v2/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Select from './src/select.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSelectV2: SFCWithInstall<typeof Select> = withInstall(Select)\nexport default ElSelectV2\n\nexport * from './src/token'\n"], "names": ["withInstall", "Select"], "mappings": ";;;;;;;;AAEY,MAAC,UAAU,GAAGA,mBAAW,CAACC,iBAAM;;;;;;"}