{"version": 3, "names": ["_lowercase", "require", "Object", "keys", "for<PERSON>ach", "key", "exports", "defineProperty", "enumerable", "get", "_uppercase"], "sources": ["../../../src/builders/generated/index.ts"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\n\nexport * from \"./lowercase.ts\";\nexport * from \"./uppercase.ts\";\n\n// Uppercase builders and AST types conflict with each other, which is\n// not allowed by TypeScript when using `export * from ...`\n// We instead explicity list the AST types here, so that:\n// - From a TypeScript perspective, the AST types win over the uppercase\n//   builders (which is the standard behavior for JS when a named\n//   re-export conflicts with a * re-export.)\n// - At runtime, this `export type` is removed, leaving only the uppercase\n//   builders behind (which are thus visible to JavaScript code).\n// This ensures compatibility with legacy code that uses the uppercase\n// builders, while allowing TypeScript users to use the lowercase builders\n// together with the AST types.\n\n// prettier-ignore\nexport type {\n  ArrayExpression, AssignmentExpression, BinaryExpression, InterpreterDirective, Directive, DirectiveLiteral, BlockStatement, BreakStatement, CallExpression, CatchClause, ConditionalExpression, ContinueStatement, DebuggerStatement, DoWhileStatement, EmptyStatement, ExpressionStatement, File, ForInStatement, ForStatement, FunctionDeclaration, FunctionExpression, Identifier, IfStatement, LabeledStatement, StringLiteral, NumericLiteral, NullLiteral, BooleanLiteral, RegExpLiteral, LogicalExpression, MemberExpression, NewExpression, Program, ObjectExpression, ObjectMethod, ObjectProperty, RestElement, ReturnStatement, SequenceExpression, ParenthesizedExpression, SwitchCase, SwitchStatement, ThisExpression, ThrowStatement, TryStatement, UnaryExpression, UpdateExpression, VariableDeclaration, VariableDeclarator, WhileStatement, WithStatement, AssignmentPattern, ArrayPattern, ArrowFunctionExpression, ClassBody, ClassExpression, ClassDeclaration, ExportAllDeclaration, ExportDefaultDeclaration, ExportNamedDeclaration, ExportSpecifier, ForOfStatement, ImportDeclaration, ImportDefaultSpecifier, ImportNamespaceSpecifier, ImportSpecifier, ImportExpression, MetaProperty, ClassMethod, ObjectPattern, SpreadElement, Super, TaggedTemplateExpression, TemplateElement, TemplateLiteral, YieldExpression, AwaitExpression, Import, BigIntLiteral, ExportNamespaceSpecifier, OptionalMemberExpression, OptionalCallExpression, ClassProperty, ClassAccessorProperty, ClassPrivateProperty, ClassPrivateMethod, PrivateName, StaticBlock, ImportAttribute, AnyTypeAnnotation, ArrayTypeAnnotation, BooleanTypeAnnotation, BooleanLiteralTypeAnnotation, NullLiteralTypeAnnotation, ClassImplements, DeclareClass, DeclareFunction, DeclareInterface, DeclareModule, DeclareModuleExports, DeclareTypeAlias, DeclareOpaqueType, DeclareVariable, DeclareExportDeclaration, DeclareExportAllDeclaration, DeclaredPredicate, ExistsTypeAnnotation, FunctionTypeAnnotation, FunctionTypeParam, GenericTypeAnnotation, InferredPredicate, InterfaceExtends, InterfaceDeclaration, InterfaceTypeAnnotation, IntersectionTypeAnnotation, MixedTypeAnnotation, EmptyTypeAnnotation, NullableTypeAnnotation, NumberLiteralTypeAnnotation, NumberTypeAnnotation, ObjectTypeAnnotation, ObjectTypeInternalSlot, ObjectTypeCallProperty, ObjectTypeIndexer, ObjectTypeProperty, ObjectTypeSpreadProperty, OpaqueType, QualifiedTypeIdentifier, StringLiteralTypeAnnotation, StringTypeAnnotation, SymbolTypeAnnotation, ThisTypeAnnotation, TupleTypeAnnotation, TypeofTypeAnnotation, TypeAlias, TypeAnnotation, TypeCastExpression, TypeParameter, TypeParameterDeclaration, TypeParameterInstantiation, UnionTypeAnnotation, Variance, VoidTypeAnnotation, EnumDeclaration, EnumBooleanBody, EnumNumberBody, EnumStringBody, EnumSymbolBody, EnumBooleanMember, EnumNumberMember, EnumStringMember, EnumDefaultedMember, IndexedAccessType, OptionalIndexedAccessType, JSXAttribute, JSXClosingElement, JSXElement, JSXEmptyExpression, JSXExpressionContainer, JSXSpreadChild, JSXIdentifier, JSXMemberExpression, JSXNamespacedName, JSXOpeningElement, JSXSpreadAttribute, JSXText, JSXFragment, JSXOpeningFragment, JSXClosingFragment, Noop, Placeholder, V8IntrinsicIdentifier, ArgumentPlaceholder, BindExpression, Decorator, DoExpression, ExportDefaultSpecifier, RecordExpression, TupleExpression, DecimalLiteral, ModuleExpression, TopicReference, PipelineTopicExpression, PipelineBareFunction, PipelinePrimaryTopicReference, VoidPattern, TSParameterProperty, TSDeclareFunction, TSDeclareMethod, TSQualifiedName, TSCallSignatureDeclaration, TSConstructSignatureDeclaration, TSPropertySignature, TSMethodSignature, TSIndexSignature, TSAnyKeyword, TSBooleanKeyword, TSBigIntKeyword, TSIntrinsicKeyword, TSNeverKeyword, TSNullKeyword, TSNumberKeyword, TSObjectKeyword, TSStringKeyword, TSSymbolKeyword, TSUndefinedKeyword, TSUnknownKeyword, TSVoidKeyword, TSThisType, TSFunctionType, TSConstructorType, TSTypeReference, TSTypePredicate, TSTypeQuery, TSTypeLiteral, TSArrayType, TSTupleType, TSOptionalType, TSRestType, TSNamedTupleMember, TSUnionType, TSIntersectionType, TSConditionalType, TSInferType, TSParenthesizedType, TSTypeOperator, TSIndexedAccessType, TSMappedType, TSTemplateLiteralType, TSLiteralType, TSExpressionWithTypeArguments, TSInterfaceDeclaration, TSInterfaceBody, TSTypeAliasDeclaration, TSInstantiationExpression, TSAsExpression, TSSatisfiesExpression, TSTypeAssertion, TSEnumBody, TSEnumDeclaration, TSEnumMember, TSModuleDeclaration, TSModuleBlock, TSImportType, TSImportEqualsDeclaration, TSExternalModuleReference, TSNonNullExpression, TSExportAssignment, TSNamespaceExportDeclaration, TSTypeAnnotation, TSTypeParameterInstantiation, TSTypeParameterDeclaration, TSTypeParameter,\n  NumberLiteral, RegexLiteral, RestProperty, SpreadProperty\n} from \"../../ast-types/generated/index.ts\";\n\n// This will re-export all the type definitions that do not conflict with\n// uppercase builders, such as aliases.\nexport type * from \"../../ast-types/generated/index.ts\";\n"], "mappings": ";;;;;AAKA,IAAAA,UAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,IAAA,CAAAH,UAAA,EAAAI,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAA,GAAA,IAAAC,OAAA,IAAAA,OAAA,CAAAD,GAAA,MAAAL,UAAA,CAAAK,GAAA;EAAAH,MAAA,CAAAK,cAAA,CAAAD,OAAA,EAAAD,GAAA;IAAAG,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAT,UAAA,CAAAK,GAAA;IAAA;EAAA;AAAA;AACA,IAAAK,UAAA,GAAAT,OAAA;AAAAC,MAAA,CAAAC,IAAA,CAAAO,UAAA,EAAAN,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAA,GAAA,IAAAC,OAAA,IAAAA,OAAA,CAAAD,GAAA,MAAAK,UAAA,CAAAL,GAAA;EAAAH,MAAA,CAAAK,cAAA,CAAAD,OAAA,EAAAD,GAAA;IAAAG,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAC,UAAA,CAAAL,GAAA;IAAA;EAAA;AAAA", "ignoreList": []}