{"version": 3, "file": "upload-list.mjs", "sources": ["../../../../../../packages/components/upload/src/upload-list.vue"], "sourcesContent": ["<template>\n  <transition-group tag=\"ul\" :class=\"containerKls\" :name=\"nsList.b()\">\n    <li\n      v-for=\"(file, index) in files\"\n      :key=\"file.uid || file.name\"\n      :class=\"[\n        nsUpload.be('list', 'item'),\n        nsUpload.is(file.status),\n        { focusing },\n      ]\"\n      tabindex=\"0\"\n      @keydown.delete=\"!disabled && handleRemove(file)\"\n      @focus=\"focusing = true\"\n      @blur=\"focusing = false\"\n      @click=\"focusing = false\"\n    >\n      <slot :file=\"file\" :index=\"index\">\n        <img\n          v-if=\"\n            listType === 'picture' ||\n            (file.status !== 'uploading' && listType === 'picture-card')\n          \"\n          :class=\"nsUpload.be('list', 'item-thumbnail')\"\n          :src=\"file.url\"\n          :crossorigin=\"crossorigin\"\n          alt=\"\"\n        />\n        <div\n          v-if=\"file.status === 'uploading' || listType !== 'picture-card'\"\n          :class=\"nsUpload.be('list', 'item-info')\"\n        >\n          <a\n            :class=\"nsUpload.be('list', 'item-name')\"\n            @click.prevent=\"handlePreview(file)\"\n          >\n            <el-icon :class=\"nsIcon.m('document')\">\n              <Document />\n            </el-icon>\n            <span\n              :class=\"nsUpload.be('list', 'item-file-name')\"\n              :title=\"file.name\"\n            >\n              {{ file.name }}\n            </span>\n          </a>\n          <el-progress\n            v-if=\"file.status === 'uploading'\"\n            :type=\"listType === 'picture-card' ? 'circle' : 'line'\"\n            :stroke-width=\"listType === 'picture-card' ? 6 : 2\"\n            :percentage=\"Number(file.percentage)\"\n            :style=\"listType === 'picture-card' ? '' : 'margin-top: 0.5rem'\"\n          />\n        </div>\n\n        <label :class=\"nsUpload.be('list', 'item-status-label')\">\n          <el-icon\n            v-if=\"listType === 'text'\"\n            :class=\"[nsIcon.m('upload-success'), nsIcon.m('circle-check')]\"\n          >\n            <circle-check />\n          </el-icon>\n          <el-icon\n            v-else-if=\"['picture-card', 'picture'].includes(listType)\"\n            :class=\"[nsIcon.m('upload-success'), nsIcon.m('check')]\"\n          >\n            <Check />\n          </el-icon>\n        </label>\n        <el-icon\n          v-if=\"!disabled\"\n          :class=\"nsIcon.m('close')\"\n          @click=\"handleRemove(file)\"\n        >\n          <Close />\n        </el-icon>\n        <!-- Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn-->\n        <!-- This is a bug which needs to be fixed -->\n        <!-- TODO: Fix the incorrect navigation interaction -->\n        <i v-if=\"!disabled\" :class=\"nsIcon.m('close-tip')\">{{\n          t('el.upload.deleteTip')\n        }}</i>\n        <span\n          v-if=\"listType === 'picture-card'\"\n          :class=\"nsUpload.be('list', 'item-actions')\"\n        >\n          <span\n            :class=\"nsUpload.be('list', 'item-preview')\"\n            @click=\"handlePreview(file)\"\n          >\n            <el-icon :class=\"nsIcon.m('zoom-in')\"><zoom-in /></el-icon>\n          </span>\n          <span\n            v-if=\"!disabled\"\n            :class=\"nsUpload.be('list', 'item-delete')\"\n            @click=\"handleRemove(file)\"\n          >\n            <el-icon :class=\"nsIcon.m('delete')\">\n              <Delete />\n            </el-icon>\n          </span>\n        </span>\n      </slot>\n    </li>\n    <slot name=\"append\" />\n  </transition-group>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  Check,\n  CircleCheck,\n  Close,\n  Delete,\n  Document,\n  ZoomIn,\n} from '@element-plus/icons-vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport ElProgress from '@element-plus/components/progress'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { uploadListEmits, uploadListProps } from './upload-list'\n\nimport type { UploadFile } from './upload'\n\ndefineOptions({\n  name: 'ElUploadList',\n})\n\nconst props = defineProps(uploadListProps)\nconst emit = defineEmits(uploadListEmits)\n\nconst { t } = useLocale()\nconst nsUpload = useNamespace('upload')\nconst nsIcon = useNamespace('icon')\nconst nsList = useNamespace('list')\nconst disabled = useFormDisabled()\n\nconst focusing = ref(false)\n\nconst containerKls = computed(() => [\n  nsUpload.b('list'),\n  nsUpload.bm('list', props.listType),\n  nsUpload.is('disabled', props.disabled),\n])\n\nconst handleRemove = (file: UploadFile) => {\n  emit('remove', file)\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;mCA6Hc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,QAAA,GAAW,aAAa,QAAQ,CAAA,CAAA;AACtC,IAAM,MAAA,MAAA,GAAS,aAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,MAAA,GAAS,aAAa,MAAM,CAAA,CAAA;AAClC,IAAA,MAAM,WAAW,eAAgB,EAAA,CAAA;AAEjC,IAAM,MAAA,QAAA,GAAW,IAAI,KAAK,CAAA,CAAA;AAE1B,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAAA,MAClC,QAAA,CAAS,EAAE,MAAM,CAAA;AAAA,MACjB,QAAS,CAAA,EAAA,CAAG,MAAQ,EAAA,KAAA,CAAM,QAAQ,CAAA;AAAA,MAClC,QAAS,CAAA,EAAA,CAAG,UAAY,EAAA,KAAA,CAAM,QAAQ,CAAA;AAAA,KACvC,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,CAAC,IAAqB,KAAA;AACzC,MAAA,IAAA,CAAK,UAAU,IAAI,CAAA,CAAA;AAAA,KACrB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}