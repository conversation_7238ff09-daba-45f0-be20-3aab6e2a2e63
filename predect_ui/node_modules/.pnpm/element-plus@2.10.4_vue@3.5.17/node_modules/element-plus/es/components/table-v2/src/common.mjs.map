{"version": 3, "file": "common.mjs", "sources": ["../../../../../../packages/components/table-v2/src/common.ts"], "sourcesContent": ["import { definePropType, mutable } from '@element-plus/utils'\n\nimport type { CSSProperties } from 'vue'\nimport type { Column, KeyType } from './types'\n\nexport type AnyColumn = Column<any>\n\n/**\n * @Note even though we can use `string[] | string` as the type but for\n * convenience here we only use `string` as the acceptable value here.\n */\nexport const classType = String\n\nexport const columns = {\n  type: definePropType<AnyColumn[]>(Array),\n  required: true,\n} as const\n\nexport const column = {\n  type: definePropType<AnyColumn>(Object),\n} as const\n\nexport const fixedDataType = {\n  type: definePropType<any[]>(Array),\n} as const\n\nexport const dataType = {\n  ...fixedDataType,\n  required: true,\n} as const\n\nexport const expandColumnKey = String\n\nexport const expandKeys = {\n  type: definePropType<KeyType[]>(Array),\n  default: () => mutable([]),\n} as const\n\nexport const requiredNumber = {\n  type: Number,\n  required: true,\n} as const\n\nexport const rowKey = {\n  type: definePropType<KeyType>([String, Number, Symbol]),\n  default: 'id',\n} as const\n\n/**\n * @note even though we can use `StyleValue` but that would be difficult for us to mapping them,\n * so we only use `CSSProperties` as the acceptable value here.\n */\nexport const styleType = {\n  type: definePropType<CSSProperties>(Object),\n}\n"], "names": [], "mappings": ";;;AACY,MAAC,SAAS,GAAG,OAAO;AACpB,MAAC,OAAO,GAAG;AACvB,EAAE,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC7B,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE;AACU,MAAC,MAAM,GAAG;AACtB,EAAE,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAC9B,EAAE;AACU,MAAC,aAAa,GAAG;AAC7B,EAAE,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC7B,EAAE;AACU,MAAC,QAAQ,GAAG;AACxB,EAAE,GAAG,aAAa;AAClB,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE;AACU,MAAC,eAAe,GAAG,OAAO;AAC1B,MAAC,UAAU,GAAG;AAC1B,EAAE,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC7B,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC5B,EAAE;AACU,MAAC,cAAc,GAAG;AAC9B,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE;AACU,MAAC,MAAM,GAAG;AACtB,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAChD,EAAE,OAAO,EAAE,IAAI;AACf,EAAE;AACU,MAAC,SAAS,GAAG;AACzB,EAAE,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAC9B;;;;"}