import { buildProps } from '../../../utils/vue/props/runtime.mjs';

const tooltipV2CommonProps = buildProps({
  nowrap: Boolean
});
var TooltipV2Sides = /* @__PURE__ */ ((TooltipV2Sides2) => {
  TooltipV2Sides2["top"] = "top";
  TooltipV2Sides2["bottom"] = "bottom";
  TooltipV2Sides2["left"] = "left";
  TooltipV2Sides2["right"] = "right";
  return TooltipV2Sides2;
})(TooltipV2Sides || {});
const tooltipV2Sides = Object.values(TooltipV2Sides);
const tooltipV2OppositeSide = {
  ["top" /* top */]: "bottom" /* bottom */,
  ["bottom" /* bottom */]: "top" /* top */,
  ["left" /* left */]: "right" /* right */,
  ["right" /* right */]: "left" /* left */
};
const tooltipV2ArrowBorders = {
  ["top" /* top */]: ["left" /* left */, "top" /* top */],
  ["bottom" /* bottom */]: ["bottom" /* bottom */, "right" /* right */],
  ["left" /* left */]: ["bottom" /* bottom */, "left" /* left */],
  ["right" /* right */]: ["top" /* top */, "right" /* right */]
};

export { TooltipV2Sides, tooltipV2ArrowBorders, tooltipV2CommonProps, tooltipV2OppositeSide, tooltipV2Sides };
//# sourceMappingURL=common.mjs.map
