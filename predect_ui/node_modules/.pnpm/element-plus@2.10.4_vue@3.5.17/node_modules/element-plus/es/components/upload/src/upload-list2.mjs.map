{"version": 3, "file": "upload-list2.mjs", "sources": ["../../../../../../packages/components/upload/src/upload-list.ts"], "sourcesContent": ["import { NOOP, buildProps, definePropType, mutable } from '@element-plus/utils'\nimport { uploadListTypes } from './upload'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { UploadFile, UploadFiles, UploadHooks } from './upload'\nimport type UploadList from './upload-list.vue'\n\nexport const uploadListProps = buildProps({\n  files: {\n    type: definePropType<UploadFiles>(Array),\n    default: () => mutable([]),\n  },\n  disabled: Boolean,\n  handlePreview: {\n    type: definePropType<UploadHooks['onPreview']>(Function),\n    default: NOOP,\n  },\n  listType: {\n    type: String,\n    values: uploadListTypes,\n    default: 'text',\n  },\n  /**\n   * @description set HTML attribute: crossorigin.\n   */\n  crossorigin: {\n    type: definePropType<'anonymous' | 'use-credentials' | ''>(String),\n  },\n} as const)\n\nexport type UploadListProps = ExtractPropTypes<typeof uploadListProps>\nexport type UploadListPropsPublic = __ExtractPublicPropTypes<\n  typeof uploadListProps\n>\nexport const uploadListEmits = {\n  remove: (file: UploadFile) => !!file,\n}\nexport type UploadListEmits = typeof uploadListEmits\nexport type UploadListInstance = InstanceType<typeof UploadList> & unknown\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,eAAe,GAAG,UAAU,CAAC;AAC1C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,eAAe;AAC3B,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,CAAC,EAAE;AACS,MAAC,eAAe,GAAG;AAC/B,EAAE,MAAM,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;AAC1B;;;;"}