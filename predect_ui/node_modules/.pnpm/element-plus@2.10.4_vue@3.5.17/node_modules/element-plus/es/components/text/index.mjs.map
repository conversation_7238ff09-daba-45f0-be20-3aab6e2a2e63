{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/text/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Text from './src/text.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElText: SFCWithInstall<typeof Text> = withInstall(Text)\nexport default ElText\n\nexport * from './src/text'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,MAAM,GAAG,WAAW,CAAC,IAAI;;;;"}