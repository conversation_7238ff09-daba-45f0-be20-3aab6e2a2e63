{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-locale/index.ts"], "sourcesContent": ["import { computed, inject, isRef, ref, unref } from 'vue'\nimport { get } from 'lodash-unified'\nimport English from '@element-plus/locale/lang/en'\n\nimport type { MaybeRef } from '@vueuse/core'\nimport type { InjectionKey, Ref } from 'vue'\nimport type { Language } from '@element-plus/locale'\n\nexport type TranslatorOption = Record<string, string | number>\nexport type Translator = (path: string, option?: TranslatorOption) => string\nexport type LocaleContext = {\n  locale: Ref<Language>\n  lang: Ref<string>\n  t: Translator\n}\n\nexport const buildTranslator =\n  (locale: MaybeRef<Language>): Translator =>\n  (path, option) =>\n    translate(path, option, unref(locale))\n\nexport const translate = (\n  path: string,\n  option: undefined | TranslatorOption,\n  locale: Language\n): string =>\n  (get(locale, path, path) as string).replace(\n    /\\{(\\w+)\\}/g,\n    (_, key) => `${option?.[key] ?? `{${key}}`}`\n  )\n\nexport const buildLocaleContext = (\n  locale: MaybeRef<Language>\n): LocaleContext => {\n  const lang = computed(() => unref(locale).name)\n  const localeRef = isRef(locale) ? locale : ref(locale)\n  return {\n    lang,\n    locale: localeRef,\n    t: buildTranslator(locale),\n  }\n}\n\nexport const localeContextKey: InjectionKey<Ref<Language | undefined>> =\n  Symbol('localeContextKey')\n\nexport const useLocale = (localeOverrides?: Ref<Language | undefined>) => {\n  const locale = localeOverrides || inject(localeContextKey, ref())!\n  return buildLocaleContext(computed(() => locale.value || English))\n}\n"], "names": [], "mappings": ";;;;AAGY,MAAC,eAAe,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM,KAAK,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE;AACxF,MAAC,SAAS,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK;AAC7G,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,CAAC,EAAE;AACS,MAAC,kBAAkB,GAAG,CAAC,MAAM,KAAK;AAC9C,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;AAClD,EAAE,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;AACzD,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,MAAM,EAAE,SAAS;AACrB,IAAI,CAAC,EAAE,eAAe,CAAC,MAAM,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,EAAE;AAC/C,MAAC,SAAS,GAAG,CAAC,eAAe,KAAK;AAC9C,EAAE,MAAM,MAAM,GAAG,eAAe,IAAI,MAAM,CAAC,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC;AACpE,EAAE,OAAO,kBAAkB,CAAC,QAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC;AACrE;;;;"}