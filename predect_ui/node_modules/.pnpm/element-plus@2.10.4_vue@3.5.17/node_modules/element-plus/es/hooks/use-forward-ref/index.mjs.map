{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-forward-ref/index.ts"], "sourcesContent": ["import { provide } from 'vue'\n\nimport type { InjectionKey, ObjectDirective, Ref } from 'vue'\n\ntype ForwardRefSetter = <T>(el: T) => void\n\nexport type ForwardRefInjectionContext = {\n  setForwardRef: ForwardRefSetter\n}\n\nexport const FORWARD_REF_INJECTION_KEY: InjectionKey<ForwardRefInjectionContext> =\n  Symbol('elForwardRef')\n\nexport const useForwardRef = <T>(forwardRef: Ref<T | null>) => {\n  const setForwardRef = (el: T) => {\n    forwardRef.value = el\n  }\n\n  provide(FORWARD_REF_INJECTION_KEY, {\n    setForwardRef,\n  })\n}\n\nexport const useForwardRefDirective = (\n  setForwardRef: ForwardRefSetter\n): ObjectDirective => {\n  return {\n    mounted(el) {\n      setForwardRef(el)\n    },\n    updated(el) {\n      setForwardRef(el)\n    },\n    unmounted() {\n      setForwardRef(null)\n    },\n  }\n}\n"], "names": [], "mappings": ";;AACY,MAAC,yBAAyB,GAAG,MAAM,CAAC,cAAc,EAAE;AACpD,MAAC,aAAa,GAAG,CAAC,UAAU,KAAK;AAC7C,EAAE,MAAM,aAAa,GAAG,CAAC,EAAE,KAAK;AAChC,IAAI,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,yBAAyB,EAAE;AACrC,IAAI,aAAa;AACjB,GAAG,CAAC,CAAC;AACL,EAAE;AACU,MAAC,sBAAsB,GAAG,CAAC,aAAa,KAAK;AACzD,EAAE,OAAO;AACT,IAAI,OAAO,CAAC,EAAE,EAAE;AAChB,MAAM,aAAa,CAAC,EAAE,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,CAAC,EAAE,EAAE;AAChB,MAAM,aAAa,CAAC,EAAE,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,SAAS,GAAG;AAChB,MAAM,aAAa,CAAC,IAAI,CAAC,CAAC;AAC1B,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}