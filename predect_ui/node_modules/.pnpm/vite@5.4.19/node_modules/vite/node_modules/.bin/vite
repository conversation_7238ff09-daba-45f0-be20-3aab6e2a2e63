#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/vite@5.4.19/node_modules/vite/bin/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/vite@5.4.19/node_modules/vite/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/vite@5.4.19/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/vite@5.4.19/node_modules/vite/bin/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/vite@5.4.19/node_modules/vite/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/vite@5.4.19/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/vite.js" "$@"
else
  exec node  "$basedir/../../bin/vite.js" "$@"
fi
