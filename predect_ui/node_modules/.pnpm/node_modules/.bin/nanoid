#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/bin/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/nanoid@3.3.11/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/bin/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/nanoid@3.3.11/node_modules:/home/<USER>/workspace/myLLM/my_LLM_study/tuning_model/predect_ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nanoid/bin/nanoid.cjs" "$@"
else
  exec node  "$basedir/../nanoid/bin/nanoid.cjs" "$@"
fi
