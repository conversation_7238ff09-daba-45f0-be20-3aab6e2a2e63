<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 20px auto; padding: 20px; }
        .test { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🚀 LaTeX OCR 快速测试</h1>
    
    <div id="results">
        <div class="info">点击下面的按钮开始测试...</div>
    </div>
    
    <button onclick="testServices()">测试服务状态</button>
    <button onclick="window.open('http://localhost:3000', '_blank')">打开前端界面</button>
    <button onclick="testAPI()">测试 API 功能</button>
    
    <div style="margin-top: 20px;">
        <h3>📋 功能清单</h3>
        <ul>
            <li>✅ 前端界面：现代化设计，图片预览</li>
            <li>✅ 图片上传：拖拽上传，文件验证</li>
            <li>✅ 状态显示：加载状态，错误提示</li>
            <li>✅ 后端 API：GPU 内存优化，稳定识别</li>
            <li>✅ 结果展示：Markdown 渲染，聊天界面</li>
        </ul>
    </div>

    <script>
        async function testServices() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">正在测试服务...</div>';
            
            const tests = [
                { name: '前端服务', url: 'http://localhost:3000', expected: 200 },
                { name: '后端 API', url: 'http://localhost:8000', expected: 404 } // 404 是正常的
            ];
            
            let results = [];
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    const success = response.status === test.expected;
                    results.push({
                        name: test.name,
                        status: success ? 'success' : 'error',
                        message: success ? `✅ 正常 (${response.status})` : `❌ 异常 (${response.status})`
                    });
                } catch (error) {
                    results.push({
                        name: test.name,
                        status: 'error',
                        message: `❌ 连接失败: ${error.message}`
                    });
                }
            }
            
            resultsDiv.innerHTML = results.map(r => 
                `<div class="${r.status}"><strong>${r.name}</strong>: ${r.message}</div>`
            ).join('');
            
            const allSuccess = results.every(r => r.status === 'success');
            if (allSuccess) {
                resultsDiv.innerHTML += '<div class="success"><strong>🎉 所有服务正常运行！</strong></div>';
            }
        }
        
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">正在测试 API 功能...</div>';
            
            try {
                // 创建一个简单的测试图片 (1x1 像素的 PNG)
                const canvas = document.createElement('canvas');
                canvas.width = 100;
                canvas.height = 50;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, 100, 50);
                ctx.fillStyle = 'black';
                ctx.font = '16px Arial';
                ctx.fillText('x²+y²=z²', 10, 30);
                
                canvas.toBlob(async (blob) => {
                    const formData = new FormData();
                    formData.append('file', blob, 'test.png');
                    formData.append('prompt', '请识别这个数学公式');
                    
                    try {
                        const response = await fetch('http://localhost:8000/predict', {
                            method: 'POST',
                            body: formData
                        });
                        
                        if (response.ok) {
                            const result = await response.json();
                            resultsDiv.innerHTML = `
                                <div class="success">✅ API 测试成功！</div>
                                <div class="info"><strong>识别结果:</strong><br>${result.markdown}</div>
                            `;
                        } else {
                            resultsDiv.innerHTML = `<div class="error">❌ API 测试失败: ${response.status}</div>`;
                        }
                    } catch (error) {
                        resultsDiv.innerHTML = `<div class="error">❌ API 请求失败: ${error.message}</div>`;
                    }
                }, 'image/png');
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ 测试准备失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
