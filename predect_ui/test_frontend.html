<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #f0f9ff; color: #1e40af; border: 1px solid #93c5fd; }
        .error { background-color: #fef2f2; color: #dc2626; border: 1px solid #fca5a5; }
        .info { background-color: #f0f9ff; color: #1e40af; border: 1px solid #93c5fd; }
        button {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #337ecc; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
    </style>
</head>
<body>
    <h1>🧪 LaTeX OCR 前端功能测试</h1>
    
    <div class="test-card">
        <h2>📋 测试清单</h2>
        <div id="test-results">
            <div class="info">正在进行测试...</div>
        </div>
        <button onclick="runTests()">重新测试</button>
        <button onclick="window.open('http://localhost:3000', '_blank')">打开前端界面</button>
    </div>

    <div class="test-card">
        <h2>📝 使用说明</h2>
        <ol>
            <li><strong>上传图片</strong>：点击或拖拽图片到上传区域</li>
            <li><strong>查看预览</strong>：上传后应该显示图片预览和文件名</li>
            <li><strong>输入提示词</strong>：例如"请识别这个数学公式"</li>
            <li><strong>点击发送</strong>：按钮应该显示"识别中..."状态</li>
            <li><strong>查看结果</strong>：识别结果会显示在聊天区域</li>
        </ol>
    </div>

    <div class="test-card">
        <h2>🔧 新增功能</h2>
        <ul>
            <li>✅ <strong>图片预览</strong>：上传后显示图片缩略图</li>
            <li>✅ <strong>上传状态</strong>：显示已选择的文件名</li>
            <li>✅ <strong>加载状态</strong>：发送时显示"识别中..."</li>
            <li>✅ <strong>错误提示</strong>：文件类型、大小验证</li>
            <li>✅ <strong>重新选择</strong>：可以删除当前图片重新上传</li>
            <li>✅ <strong>按钮状态</strong>：只有上传图片且输入提示词后才能发送</li>
            <li>✅ <strong>响应式设计</strong>：支持手机端访问</li>
        </ul>
    </div>

    <script>
        async function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="info">正在测试...</div>';
            
            const tests = [
                {
                    name: '前端服务连接',
                    test: () => fetch('http://localhost:3000').then(r => r.ok)
                },
                {
                    name: '后端 API 连接',
                    test: () => fetch('http://localhost:8000').then(r => r.status === 404) // 404 是正常的，说明服务在运行
                },
                {
                    name: 'Vue.js 应用加载',
                    test: () => fetch('http://localhost:3000/src/main.js').then(r => r.ok)
                },
                {
                    name: 'Element Plus 样式',
                    test: () => fetch('http://localhost:3000/node_modules/element-plus/dist/index.css').then(r => r.ok)
                }
            ];

            let results = [];
            
            for (const test of tests) {
                try {
                    const result = await test.test();
                    results.push({
                        name: test.name,
                        status: result ? 'success' : 'error',
                        message: result ? '✅ 通过' : '❌ 失败'
                    });
                } catch (error) {
                    results.push({
                        name: test.name,
                        status: 'error',
                        message: `❌ 错误: ${error.message}`
                    });
                }
            }

            // 显示结果
            resultsDiv.innerHTML = results.map(r => 
                `<div class="${r.status}"><strong>${r.name}</strong>: ${r.message}</div>`
            ).join('');

            // 添加总结
            const passed = results.filter(r => r.status === 'success').length;
            const total = results.length;
            const summary = passed === total ? 
                `<div class="success"><strong>🎉 所有测试通过 (${passed}/${total})</strong></div>` :
                `<div class="error"><strong>⚠️ 部分测试失败 (${passed}/${total})</strong></div>`;
            
            resultsDiv.innerHTML += summary;
        }

        // 页面加载时自动运行测试
        window.onload = runTests;
    </script>
</body>
</html>
