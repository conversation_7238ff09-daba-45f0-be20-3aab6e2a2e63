#!/bin/bash

echo "=== LaTeX OCR 内存优化启动脚本 ==="

# 设置 CUDA 内存优化环境变量
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export CUDA_LAUNCH_BLOCKING=1

# 检查是否安装了 conda
if ! command -v conda &> /dev/null; then
    echo "错误: 未找到 conda，请先安装 Anaconda 或 Miniconda"
    exit 1
fi

# 激活 tuning_model 虚拟环境
echo "正在激活 tuning_model 虚拟环境..."
source ~/workspace/anaconda3/etc/profile.d/conda.sh
conda activate tuning_model

# 检查虚拟环境是否激活成功
if [[ "$CONDA_DEFAULT_ENV" != "tuning_model" ]]; then
    echo "错误: 无法激活 tuning_model 虚拟环境"
    exit 1
fi

echo "已激活虚拟环境: $CONDA_DEFAULT_ENV"

# 清理 GPU 内存
echo "正在清理 GPU 内存..."
python -c "import torch; torch.cuda.empty_cache() if torch.cuda.is_available() else None; print('GPU 内存已清理')"

# 进入项目目录
cd predect_ui

# 安装前端依赖
echo "正在检查前端依赖..."
if [ ! -d "node_modules" ]; then
    echo "正在安装前端依赖..."
    npm install
fi

# 启动后端服务
echo "正在启动后端 API 服务（内存优化模式）..."
python 1.api.py &
API_PID=$!

# 等待后端启动
echo "等待后端服务启动..."
sleep 10

# 检查后端是否启动成功
if curl -s http://localhost:8000 > /dev/null; then
    echo "✓ 后端服务启动成功"
else
    echo "✗ 后端服务启动失败"
    kill $API_PID 2>/dev/null
    exit 1
fi

# 启动前端服务
echo "正在启动前端开发服务器..."
npm run dev &
FRONTEND_PID=$!

echo "=== 服务启动完成 ==="
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:8000"
echo ""
echo "内存优化设置已启用："
echo "- PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True"
echo "- 使用 float16 精度"
echo "- 减少生成长度到 256 tokens"
echo "- 启用自动内存清理"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $API_PID $FRONTEND_PID 2>/dev/null; exit" INT
wait
