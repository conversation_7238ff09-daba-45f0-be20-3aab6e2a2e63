{"alpha_pattern": {}, "auto_mapping": null, "base_model_name_or_path": "./Qwen/Qwen2-VL-2B-Instruct", "bias": "none", "fan_in_fan_out": false, "inference_mode": true, "init_lora_weights": true, "layer_replication": null, "layers_pattern": null, "layers_to_transform": null, "loftq_config": {}, "lora_alpha": 16, "lora_dropout": 0.05, "megatron_config": null, "megatron_core": "megatron.core", "modules_to_save": null, "peft_type": "LORA", "r": 64, "rank_pattern": {}, "revision": null, "target_modules": ["v_proj", "up_proj", "q_proj", "gate_proj", "o_proj", "k_proj", "down_proj"], "task_type": "CAUSAL_LM", "use_dora": false, "use_rslora": false}