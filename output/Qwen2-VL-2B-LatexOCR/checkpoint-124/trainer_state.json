{"best_metric": null, "best_model_checkpoint": null, "epoch": 1.9919678714859437, "eval_steps": 500, "global_step": 124, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.01606425702811245, "grad_norm": 2.3005013465881348, "learning_rate": 9.919354838709678e-05, "loss": 0.3355, "step": 1}, {"epoch": 0.1606425702811245, "grad_norm": 1.1149388551712036, "learning_rate": 9.193548387096774e-05, "loss": 0.1446, "step": 10}, {"epoch": 0.321285140562249, "grad_norm": 0.4606121778488159, "learning_rate": 8.387096774193549e-05, "loss": 0.0995, "step": 20}, {"epoch": 0.4819277108433735, "grad_norm": 0.7274818420410156, "learning_rate": 7.580645161290323e-05, "loss": 0.0968, "step": 30}, {"epoch": 0.642570281124498, "grad_norm": 0.5126323699951172, "learning_rate": 6.774193548387096e-05, "loss": 0.0797, "step": 40}, {"epoch": 0.8032128514056225, "grad_norm": 0.7756349444389343, "learning_rate": 5.9677419354838715e-05, "loss": 0.0886, "step": 50}, {"epoch": 0.963855421686747, "grad_norm": 0.5941352248191833, "learning_rate": 5.161290322580645e-05, "loss": 0.1051, "step": 60}, {"epoch": 1.1244979919678715, "grad_norm": 0.5419469475746155, "learning_rate": 4.3548387096774194e-05, "loss": 0.0702, "step": 70}, {"epoch": 1.285140562248996, "grad_norm": 0.8999052047729492, "learning_rate": 3.548387096774194e-05, "loss": 0.0944, "step": 80}, {"epoch": 1.4457831325301205, "grad_norm": 0.563507616519928, "learning_rate": 2.7419354838709678e-05, "loss": 0.0646, "step": 90}, {"epoch": 1.606425702811245, "grad_norm": 0.8440276384353638, "learning_rate": 1.935483870967742e-05, "loss": 0.0623, "step": 100}, {"epoch": 1.7670682730923695, "grad_norm": 0.8753255009651184, "learning_rate": 1.129032258064516e-05, "loss": 0.0681, "step": 110}, {"epoch": 1.927710843373494, "grad_norm": 0.7876906394958496, "learning_rate": 3.225806451612903e-06, "loss": 0.0651, "step": 120}], "logging_steps": 10, "max_steps": 124, "num_input_tokens_seen": 0, "num_train_epochs": 2, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1.115740197218304e+16, "train_batch_size": 2, "trial_name": null, "trial_params": null}