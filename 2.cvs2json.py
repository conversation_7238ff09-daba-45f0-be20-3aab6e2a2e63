import pandas as pd  # 导入 pandas 用于数据处理
import json          # 导入 json 模块用于数据序列化保存

# 定义CSV文件路径（包含图像路径和对应文本的标注数据）
csv_path = './latex_ocr_train.csv'

# 定义生成的训练集和验证集 JSON 文件路径
train_json_path = './latex_ocr_train.json'
val_json_path = './latex_ocr_val.json'

# 使用 pandas 读取CSV文件，生成DataFrame对象，包含 image_path 和 text 两列
df = pd.read_csv(csv_path)

# 用于存储转换后的对话格式数据（符合对话型大模型训练要求）
conversations = []

# 遍历每一行数据，将图像路径与标注文本转换为 user-assistant 对话结构
for i in range(len(df)):
    conversations.append({
        "id": f"identity_{i+1}",  # 为每条对话分配唯一的身份标识
        "conversations": [
            {
                "role": "user",  # 用户角色提出问题，这里是图片路径，表示“请识别该图像内容”
                "value": f"{df.iloc[i]['image_path']}"
            },
            {
                "role": "assistant",  # 助手角色回答问题，这里是识别出的 LaTeX 文本内容
                "value": str(df.iloc[i]['text'])
            }
        ]
    })

# 将前面的数据集划分为训练集和验证集
# 默认将最后4条数据作为验证集，其余作为训练集（可根据需要调整）
train_conversations = conversations[:-4]  # 训练集：从第1条到倒数第5条（包含）
val_conversations = conversations[-4:]   # 验证集：最后4条

# 将训练集保存为 JSON 文件，使用 UTF-8 编码，格式化缩进便于阅读
with open(train_json_path, 'w', encoding='utf-8') as file:
    json.dump(train_conversations, file, ensure_ascii=False, indent=2)

# 将验证集保存为 JSON 文件，格式相同
with open(val_json_path, 'w', encoding='utf-8') as file:
    json.dump(val_conversations, file, ensure_ascii=False, indent=2)
