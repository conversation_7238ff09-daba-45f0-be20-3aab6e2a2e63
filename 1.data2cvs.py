# 导入所需的库
from modelscope.msdatasets import MsDataset
import os
import pandas as pd

# ==================================
from utils import set_proxy

set_proxy()  # 设置代理
# ==================================

MAX_DATA_NUMBER = 1000  # 数据集数量
dataset_id = 'Qwen/Qwen2.5-VL-7B-Instruct'  # 'AI-ModelScope/LaTeX_OCR'
subset_name = 'default'  # 数据集子集名称
split = 'train'  # 数据集的划分

dataset_dir = 'LaTeX_OCR'  # 数据集保存目录
csv_path = './latex_ocr_train.csv'  # 保存的CSV文件路径
# 检查目录是否已存在
if not os.path.exists(dataset_dir):
    # 从modelscope下载LaTeX OCR数据集, 并保存为CSV文件
    from modelscope.utils.file_utils import get_modelscope_cache_dir

    print(f'正在下载数据集{dataset_id}...,数据集存放在{get_modelscope_cache_dir()}')
    ds = MsDataset.load(dataset_id, subset_name=subset_name, split=split)
    print(f'数据集大小为{len(ds)}')
    print(f'数据集0索引的信息为{ds[0]}')
    # 设置处理的图片数量上限
    total = min(MAX_DATA_NUMBER, len(ds))

    # 创建保存图片的目录
    os.makedirs(dataset_dir, exist_ok=True)

    # 初始化存储图片路径和描述的列表
    image_paths = []
    texts = []

    for i in range(total):
        # 获取每个样本的信息
        item = ds[i]
        text = item['text']  # 提取描述
        image = item['image']  # 提取图片

        # 保存图片并记录路径
        image_path = os.path.abspath(f'{dataset_dir}/{i}.jpg')  # 图片保存路径
        image.save(image_path)

        # 将路径和描述添加到列表中
        image_paths.append(image_path)
        texts.append(text)

        # 每处理50张图片打印一次进度
        if (i + 1) % 50 == 0:
            print(f'Processing {i + 1}/{total} images ({(i + 1) / total * 100:.1f}%)')

    # 将图片路径和描述保存为CSV文件
    df = pd.DataFrame({
        'image_path': image_paths,
        'text': texts,
    })

    # 将数据保存为CSV文件
    df.to_csv(csv_path, index=False)

    print(f'数据处理完成，共处理了{total}张图片')

else:
    print(f'{dataset_dir}目录已存在,跳过数据处理步骤')
